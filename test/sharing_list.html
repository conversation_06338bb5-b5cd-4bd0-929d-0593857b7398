<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>附件4：第三方信息共享清单</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      line-height: 1.8;
      margin: 0;
      padding: 20px;
      background-color: #f8f9fa;
      color: #333;
    }

    .container {
      /* max-width: 800px;
      margin: 0 auto;
      background-color: #fff;
      padding: 25px 40px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); */
    }

    h1,
    h2,
    h3 {
      /* color: #0056b3;
      border-bottom: 2px solid #e9ecef;
      padding-bottom: 10px;
      text-indent: 0; */
    }

    h1 {
      text-align: center;
      font-size: 1.8em;
    }

    h2 {
      font-size: 1.5em;
      margin-top: 40px;
    }

    h3 {
      font-size: 1.2em;
      margin-top: 30px;
      border-bottom: 1px solid #e9ecef;
    }

    p {
      margin-top: 15px;
      margin-bottom: 15px;
      text-indent: 2em;
    }

    a {
      /* color: #007bff; */
      text-decoration: none;
    }

    a:hover {
      text-decoration: underline;
    }

    /* --- 表格样式修改 --- */
    .table-wrapper {
      width: 100%;
      overflow-x: auto;
      /* 关键：允许表格横向滚动 */
      margin-top: 20px;
      margin-bottom: 20px;
    }

    table {
      width: 100%;
      min-width: 960px;
      /* 关键：设置表格最小宽度，防止过度压缩 */
      border-collapse: collapse;
    }

    th,
    td {
      border: 1px solid #dee2e6;
      padding: 12px;
      text-align: left;
      vertical-align: top;
    }

    th {
      background-color: #f2f2f2;
      font-weight: bold;
      white-space: nowrap;
      /* 关键：强制表头不换行 */
    }

    .no-indent {
      text-indent: 0;
    }
  </style>
</head>

<body>
  <div class="container">
    <h1>附件4：第三方信息共享清单</h1>

    <h2>SDK清单</h2>
    <div class="table-wrapper">
      <table border="1" cellpadding="5" cellspacing="0">
        <thead>
          <tr>
            <th>序号</th>
            <th>SDK名称</th>
            <th>第三方名称</th>
            <th>共享信息种类</th>
            <th>共享目的</th>
            <th>共享方式</th>
            <th>第三方隐私链接</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>1</td>
            <td>中国移动认证SDK</td>
            <td>中移互联网有限公司</td>
            <td>
              手机号码、设备参数及系统信息（设备类型、设备名称、设备型号、设备品牌、操作系统、操作系统版本、手机制造商）、网络相关信息（网络类型、网络运营商、网络制式、SIM卡状态、IP地址）爱奇艺App安装信息（名称、版本等）
            </td>
            <td>手机号码一键登录</td>
            <td>SDK本机采集</td>
            <td><a href="https://wap.cmpassport.com/resources/html/contract.html" target="_blank"
                rel="noopener noreferrer">https://wap.cmpassport.com/resources/html/contract.html</a></td>
          </tr>
        </tbody>
      </table>
    </div>

    <h2>其它共享方清单</h2>
    <h3>（一）关联方共享个人信息清单</h3>
    <div class="table-wrapper">
      <table border="1" cellpadding="5" cellspacing="0">
        <thead>
          <tr>
            <th>序号</th>
            <th>关联方名称</th>
            <th>共享信息种类</th>
            <th>共享目的</th>
            <th>共享方式</th>
            <th>关联方隐私链接</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>1</td>
            <td>青岛海尔光伏新能源有限公司</td>
            <td>用户姓名、所属部门、手机号码</td>
            <td>集团招聘、员工管理</td>
            <td>内部数据共享</td>
            <td><a href="https://support.weixin.qq.com/cgi-bin/mmsupportacctnodeweb-bin/pages/RYiYJkLOrQwu0nb8"
                target="_blank"
                rel="noopener noreferrer">https://support.weixin.qq.com/cgi-bin/mmsupportacctnodeweb-bin/pages/RYiYJkLOrQwu0nb8</a>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <h3>（二）其它第三方共享个人信息清单</h3>
    <div class="table-wrapper">
      <table border="1" cellpadding="5" cellspacing="0">
        <thead>
          <tr>
            <th>序号</th>
            <th>第三方名称</th>
            <th>共享信息种类</th>
            <th>共享目的</th>
            <th>共享方式</th>
            <th>第三方隐私链接</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>1</td>
            <td>/</td>
            <td>/</td>
            <td>/</td>
            <td>/</td>
            <td>/</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</body>

</html>
