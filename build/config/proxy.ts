import type { ProxyOptions } from 'vite'

export function createViteProxy(isUseProxy = true, proxyType: ProxyType) {
  if (!isUseProxy) return undefined

  const proxyConfig = getProxyConfig(proxyType)

  const proxy: Record<string, string | ProxyOptions> = Array.isArray(proxyConfig)
    ? proxyConfig.reduce((result: Record<string, string | ProxyOptions>, curr) => {
        result[curr.prefix] = {
          target: curr.target,
          changeOrigin: true,
          rewrite: curr.rewrite,
        }
        return result
      }, {})
    : {
        [proxyConfig.prefix]: {
          target: proxyConfig.target,
          changeOrigin: true,
          // rewrite: (path: string) => path.replace(new RegExp(`^${proxyConfig.prefix}`), '/'),
        },
      }
  return proxy
}
const proxyConfigMappings: Record<ProxyType, ProxyConfig | ProxyConfig[]> = {
  dev: [
    {
      prefix: '/merchant',
      target: 'http://operation.xiaoxianglink.com/',
    },
    {
      prefix: '/hdsapi',
      target: 'http://operation.xiaoxianglink.com/',
    },
  ],
  staging: {
    prefix: '/hdsapi',
    target: 'http://operation.xiaoxianglink.com',
  },
  prod: {
    prefix: '/api',
    target: 'http://localhost:8080',
  },
}

export function getProxyConfig(envType: ProxyType = 'dev'): ProxyConfig | ProxyConfig[] {
  return proxyConfigMappings[envType]
}
