import { resolve } from 'node:path'
import { uniuseAutoImports } from '@uni-helper/uni-use'
import Components from '@uni-helper/vite-plugin-uni-components'
import {
  UniUIResolver,
  WotResolver,
} from '@uni-helper/vite-plugin-uni-components/resolvers'
import AutoImport from 'unplugin-auto-import/vite'
import IconsResolver from 'unplugin-icons/resolver'
import icons from 'unplugin-icons/vite'
import VueMacros from 'unplugin-vue-macros/vite'
import { getRootPath, getSrcPath } from '../utils'

export default [
  AutoImport({
    imports: ['vue', 'pinia', 'uni-app', uniuseAutoImports()],
    dirs: [resolve(getSrcPath(), 'composables')],
    vueTemplate: true,
    dts: resolve(getRootPath(), 'typings/auto-import.d.ts'),
  }),
  Components({
    resolvers: [WotResolver(), UniUIResolver(), IconsResolver()],
    extensions: ['vue'],
    deep: true,
    directoryAsNamespace: true,
    dts: resolve(getRootPath(), 'typings/components.d.ts'),
  }),
  icons({
    compiler: 'vue3',
  }),
  VueMacros(),
]
