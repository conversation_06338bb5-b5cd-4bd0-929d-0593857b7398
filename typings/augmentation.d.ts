// typings/uni-network-augmentation.d.ts
import type { UnConfig, UnData } from '@uni-helper/uni-network'

declare module '@uni-helper/uni-network' {
  interface UnConfigExtend<T = UnData, D = UnData> extends UnConfig {
    isReturnNativeData?: boolean
  }

  interface UnInstance<T = UnData, D = UnData> {
    request: <TT = T, DD = D>(
      configOrUrl: string | UnConfigExtend<TT, DD>,
      config?: UnConfigExtend<TT, DD>,
    ) => Promise<TT>
    download: <TT = T, DD = D>(
      configOrUrl: string | UnConfigExtend<TT, DD>,
      config?: UnConfigExtend<TT, DD>,
    ) => Promise<TT>
    upload: <TT = T, DD = D>(
      configOrUrl: string | UnConfigExtend<TT, DD>,
      config?: UnConfigExtend<TT, DD>,
    ) => Promise<TT>
    get: <TT = T, DD = D>(url: string, config?: UnConfigExtend<TT, DD>) => Promise<TT>
    delete: <TT = T, DD = D>(url: string, config?: UnConfigExtend<TT, DD>) => Promise<TT>
    head: <TT = T, DD = D>(url: string, config?: UnConfigExtend<TT, DD>) => Promise<TT>
    options: <TT = T, DD = D>(url: string, config?: UnConfigExtend<TT, DD>) => Promise<TT>
    trace: <TT = T, DD = D>(url: string, config?: UnConfigExtend<TT, DD>) => Promise<TT>
    connect: <TT = T, DD = D>(url: string, config?: UnConfigExtend<TT, DD>) => Promise<TT>
    post: <TT = T, DD = D>(url: string, data?: DD, config?: UnConfigExtend<TT, DD>) => Promise<TT>
    put: <TT = T, DD = D>(url: string, data?: DD, config?: UnConfigExtend<TT, DD>) => Promise<TT>
    patch: <TT = T, DD = D>(url: string, data?: DD, config?: UnConfigExtend<TT, DD>) => Promise<TT>
  }
}

declare module 'wot-design-uni/components/wd-form/types' {
  interface FormItemRule {
    [key: string]: any
    required?: boolean
    message: string
    pattern?: RegExp
    validator?: (
      value: any,
      rule: FormItemRuleWithoutValidator,
    ) => boolean | Promise<string> | Promise<boolean> | Promise<void> | Promise<unknown>
  }
}
