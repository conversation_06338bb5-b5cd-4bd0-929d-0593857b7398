/**
 * 解决方案详情
 */
export interface Solution {
  /**
   * ID
   */
  id?: any
  /**
   * 方案名称
   */
  name?: string
  /**
   * 附件，[{ name: '文件名', url: '文件地址' }]
   */
  file?: any
  /**
   * 视频，[{ name: '视频名', url: '视频地址' }]
   */
  video?: any
  /**
   * 创建时间
   */
  createdAt?: string
  /**
   * 分类名称
   */
  categoryName?: string
  /**
   * 方案类型
   */
  solutionType?: string
  /**
   * 方案描述
   */
  description?: string
}

/**
 * 解决方案分页查询参数
 */
export interface SolutionPageParams {
  /**
   * 页码
   */
  pageNum?: number
  /**
   * 每页数量
   */
  pageSize?: number
  /**
   * 方案名称
   */
  name?: string
  /**
   * 分类ID
   */
  categoryId?: any
  /**
   * 方案类型
   */
  solutionType?: string
}

/**
 * 获取解决方案详情参数
 */
export interface SolutionGetParams {
  /**
   * 解决方案ID
   */
  id?: any
}

/**
 * 解决方案列表查询参数 (通常用于非分页列表)
 */
export interface SolutionListParams {
  /**
   * 方案名称
   */
  name?: string
  /**
   * 分类ID
   */
  categoryId?: any
  /**
   * 方案类型
   */
  solutionType?: string
}

export interface SolutionCategory {
  /**
   * 创建时间
   */
  createdAt?: string
  /**
   * 创建人
   */
  createdBy?: string
  /**
   * 分类ID
   */
  id?: number
  /**
   * 分类层级，从1开始
   */
  level?: number
  /**
   * 分类名称
   */
  name?: string
  /**
   * 父节点ID，根节点为0
   */
  parentId?: number
  /**
   * 分类层级路径，格式：1,2,3
   */
  path?: string
  /**
   * 方案类型，表示属于哪个类别
   */
  solutionType?: string
  /**
   * 排序号
   */
  sort?: number
  /**
   * 修改时间
   */
  updatedAt?: string
  /**
   * 修改人
   */
  updatedBy?: string
}
