export interface Message {
  attachmentName: string
  attachmentUrl: string
  content: string
  createdAt: string
  createdBy: string
  createdByName: string
  id: number
  messageType: string
  roleCodes: string[]
  sendTime: string
  status: string
  subCenterCodes: string[]
  subject: string
  updatedAt: string
  updatedBy: string
  userIds: string[]
  /** 已读状态：READ-已读，UNREAD-未读" */
  readStatus: string
}

export interface MessagePageParams {
  createdBy?: string
  endTime?: string
  messageType?: string
  pageNum?: number
  pageSize?: number
  roleCode?: string
  startTime?: string
  status?: string
  subCenterCode?: string
  subject?: string
  userId?: string
}

export interface MessageBatchMarkReadParams {
  messageIds: number[]
}
