/**
 * 分页查询考试列表的查询参数
 */
export interface ExamQuery {
  pageNum: number
  pageSize: number
  name?: string
  status?: string
  userExamStatus?: string
}

/**
 * 根据考试ID和用户ID获取答卷的查询参数
 */
export interface GetAnswerByExamAndUserParams {
  examId: number
  userId: string
}

/**
 * 获取答卷详情的查询参数
 */
export interface GetAnswerDetailParams {
  answerId: number
}

/**
 * 获取或创建答卷的查询参数
 */
export interface GetOrCreateAnswerParams {
  examId: number
}

/**
 * 根据考试ID获取所有答卷的查询参数
 */
export interface GetAnswersByExamParams {
  examId: number
}

/**
 * 考试问题选项
 */
export interface ExamQuestionOption {
  id: number
  content: string
  optionKey: string
  isCorrect?: boolean
  isSelected?: boolean
}

/**
 * 考试问题
 */
export interface ExamQuestion {
  id: number
  type: string
  title: string
  content?: string
  score?: number
  answer?: string
  analysis?: string
  options: ExamQuestionOption[]
  userAnswer?: string
}

/**
 * 考试答卷详情
 */
export interface ExamAnswerDetail {
  id: number
  examId: number
  examName: string
  startTime: string
  endTime?: string
  duration: number
  status: number
  score?: number
  totalScore?: number
  passScore?: number
  answers: Record<string, any>
  questions: ExamQuestion[]
}

/**
 * 考试答卷
 */
export interface ExamAnswer {
  id: number
  examId: number
  userId: string
  status: string
  score: number
  startTime: string
  endTime: string
  progress: number
}

/**
 * 提交答案的请求体
 */
export interface ExamAnswerRequest {
  id: number
  answerData: Record<string, string>
}

export interface LightOperationExamBankRel {
  [key: string]: any
}

export interface Exam {
  answerId?: number
  bankRels?: LightOperationExamBankRel[]
  createdAt?: string
  createdBy?: string
  delFlag?: number
  duration?: number
  endTime?: string
  id?: number
  name?: string
  passScore?: number
  questionCount?: number
  roleCodes?: string[]
  startTime?: string
  status?: string
  subCenterCodes?: string[]
  totalScore?: number
  updatedAt?: string
  updatedBy?: string
  userExamStatus?: string
  userScore?: number
}

export interface ExamPageQuery {
  createdBy?: string
  currentTime?: string
  examIds?: number[]
  name?: string
  pageNum?: number
  pageSize?: number
  status?: string
  userExamStatus?: string
  userId?: string
  userRoles?: string[]
  userSubCenterCodes?: string[]
}

/**
 * 练习结果展示对象
 */
export interface PracticeResult {
  /**
   * 涉及的题库名称列表
   */
  bankNames: string[]
  /**
   * 多选题数量
   */
  multipleChoiceCount: number
  /**
   * 练习ID
   */
  practiceId: string
  /**
   * 题目列表
   */
  questions: ExamQuestion[]
  /**
   * 单选题数量
   */
  singleChoiceCount: number
  /**
   * 练习开始时间戳
   */
  startTime: number
  /**
   * 练习状态
   */
  status: string
  /**
   * 总题数
   */
  totalCount: number
}

/**
 * 练习记录实体
 */
export interface Practice {
  /**
   * 答题数据
   */
  answerData?: string
  /**
   * 已答题数
   */
  answeredCount?: number
  /**
   * 题库ID列表
   */
  bankIds?: string
  /**
   * 题库名称列表
   */
  bankNames?: string
  /**
   * 创建时间
   */
  createdAt?: string
  /**
   * 创建人
   */
  createdBy?: string
  /**
   * 主键ID
   */
  id?: number
  /**
   * 最后更新时间
   */
  lastUpdateTime?: string
  /**
   * 多选题数量
   */
  multipleChoiceCount?: number
  /**
   * 题目数据
   */
  questionData?: string
  /**
   * 单选题数量
   */
  singleChoiceCount?: number
  /**
   * 开始时间
   */
  startTime?: string
  /**
   * 练习状态
   */
  status?: string
  /**
   * 总题数
   */
  totalCount?: number
  /**
   * 更新时间
   */
  updatedAt?: string
  /**
   * 更新人
   */
  updatedBy?: string
  /**
   * 用户ID
   */
  userId?: string
  /**
   * 用户姓名
   */
  userName?: string
}

/**
 * 完成练习的请求参数
 */
export interface CompletePracticeParams {
  /**
   * 练习ID
   */
  practiceId: number
}

/**
 * 删除练习的请求参数
 */
export interface DeletePracticeParams {
  /**
   * 练习ID
   */
  practiceId: number
}

/**
 * 获取练习详情的请求参数
 */
export interface GetPracticeDetailParams {
  /**
   * 练习ID
   */
  practiceId: number
}

/**
 * 生成练习题目的请求体
 */
export interface GeneratePracticeRequest {
  /**
   * 题库ID列表
   */
  bankIds: number[]
  /**
   * 难度过滤
   */
  difficulties?: number[]
  /**
   * 题目类型过滤
   */
  questionTypes?: string[]
  /**
   * 是否随机排序
   */
  randomOrder?: boolean
}

/**
 * 获取练习基本信息的请求参数
 */
export interface GetPracticeInfoParams {
  /**
   * 练习ID
   */
  practiceId: number
}

/**
 * 更新练习答题进度的请求体
 */
export interface UpdatePracticeProgressRequest {
  /**
   * 答题数据，格式：{questionId: answer}
   */
  answerData?: Record<string, any>
  /**
   * 练习ID
   */
  practiceId?: number
}

/**
 * 题库实体
 */
export interface QuestionBank {
  /**
   * 分类ID
   */
  categoryId?: number
  /**
   * 分类名称
   */
  categoryName?: string
  /**
   * 创建时间
   */
  createdAt?: string
  /**
   * 创建人
   */
  createdBy?: string
  /**
   * 删除标识：0-正常，1-已删除
   */
  delFlag?: number
  /**
   * 主键ID
   */
  id?: number
  /**
   * 多选题数量
   */
  multipleChoiceCount?: number
  /**
   * 题库名称
   */
  name?: string
  /**
   * 单选题数量
   */
  singleChoiceCount?: number
  /**
   * 总题数
   */
  totalCount?: number
  /**
   * 更新时间
   */
  updatedAt?: string
  /**
   * 更新人
   */
  updatedBy?: string
}

/**
 * 获取题库列表的查询参数
 */
export interface QuestionBankListParams {
  /**
   * 分类ID
   */
  categoryId?: number
  /**
   * 题库名称
   */
  name?: string
  /**
   * 页码
   */
  pageNum?: number
  /**
   * 一页数据量
   */
  pageSize?: number
}
