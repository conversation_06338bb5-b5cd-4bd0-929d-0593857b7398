/**
 * 聊天消息
 */
export interface ChatMessage {
  /**
   * AI消息
   */
  aiMessage?: string
  /**
   * 会话ID
   */
  conversationId?: string
  /**
   * 消息ID
   */
  messageId?: string
  /**
   * 消息时间
   */
  messageTime?: string
  /**
   * 消息类型user, assistant, template
   */
  messageType?: string
  /**
   * 用户ID
   */
  userId?: string
  /**
   * 用户消息
   */
  userMessage?: string
  /**
   * 消息模板
   */
  templates: ChatTemplate[]
  key?: string
}

/**
 * 客户端聊天请求
 */
export interface ClientChatRequest {
  /**
   * 会话ID
   */
  conversationId?: string
  /**
   * 消息
   */
  message?: string
  /**
   * 是否流式
   */
  streaming?: boolean
}

/**
 * 聊天响应
 */
export interface ChatResponse {
  /**
   * 消息
   */
  answer?: string
  /**
   * 会话ID
   */
  conversation_id?: string
  /**
   * 创建时间
   */
  created_at?: string
  /**
   * 消息ID
   */
  message_id?: string
  /**
   * 元数据
   */
  metadata?: object
}

/**
 * SSE Emitter
 */
export interface SseEmitter {
  timeout?: number
}

/**
 * 聊天模板
 */
export interface ChatTemplate {
  category?: string
  createdBy?: string
  createdTime?: string
  delFlag?: number
  description?: string
  id?: number
  question?: string
  sortOrder?: number
  status?: number
  updatedBy?: string
  updatedTime?: string
  useCount?: number
}

/**
 * 获取聊天记录参数
 */
export interface GetChatHistoryParams {
  /**
   * 用户ID
   */
  userId: string
}

/**
 * 清除聊天记录参数
 */
export interface ClearChatHistoryParams {
  /**
   * 用户ID
   */
  userId: string
}

/**
 * 分页获取聊天记录参数
 */
export interface GetChatHistoryPageParams {
  /**
   * 页码
   */
  pageNum?: number
  /**
   * 每页数量
   */
  pageSize?: number
}

/**
 * 重新生成答案参数
 */
export interface RegenerateAnswerParams {
  /**
   * 消息ID
   */
  messageId: string
}
