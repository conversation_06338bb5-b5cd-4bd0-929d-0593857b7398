/**
 * Payload for the login function.
 * Contains credentials and userType to determine auth strategy.
 */
export interface LoginPayload {
  userType?: 'merchant' | 'haier'
  [key: string]: any
}

/**
 * Response structure for the login API endpoint.
 * This is typically an OAuth token response.
 */
export interface LoginResponse {
  access_token: string
  token_type: 'bearer'
  refresh_token: string
  expires_in: number
  scope: string
  member_id: number
  head_image_url: string
  login_type: string
  name: string
  mobile: string
  lntoken: string
  jti: string
}
