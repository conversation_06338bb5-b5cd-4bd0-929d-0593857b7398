/**
 * @description 字典项实体类
 */
export interface DictItem {
  /**
   * @description 创建人
   */
  createdBy?: string
  /**
   * @description 创建时间
   */
  createdTime?: string
  /**
   * @description 删除状态(0-正常,1-已删除)
   */
  delFlag?: number
  /**
   * @description 描述
   */
  description?: string
  /**
   * @description 所属字典编码
   */
  dictCode?: string
  /**
   * @description 字典ID
   */
  dictId?: number
  /**
   * @description 字典项ID
   */
  id?: number
  /**
   * @description 字典项文本
   */
  itemText?: string
  /**
   * @description 字典项值
   */
  itemValue?: string
  /**
   * @description 排序
   */
  sortOrder?: number
  /**
   * @description 状态(1-启用,0-不启用)
   */
  status?: number
  /**
   * @description 修改人
   */
  updatedBy?: string
  /**
   * @description 修改时间
   */
  updatedTime?: string
}
