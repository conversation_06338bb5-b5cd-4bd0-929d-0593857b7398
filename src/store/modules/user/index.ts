import type { UserInfo } from '@/types/api/User'
import { defineStore } from 'pinia'

export const useUserStore = defineStore(
  'user',
  () => {
    const userInfo = ref<UserInfo>()
    const setUserInfo = (user: UserInfo) => {
      userInfo.value = user
    }
    const cleanUserInfo = () => {
      userInfo.value = undefined
    }
    return {
      userInfo,
      setUserInfo,
      cleanUserInfo,
    }
  },
  {
    unistorage: true,
  },
)
