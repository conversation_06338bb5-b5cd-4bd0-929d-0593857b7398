import { DefaultToken } from '@/constants'
import { defineStore } from 'pinia'

export const useAuthStore = defineStore(
  'auth',
  () => {
    const token = ref<string>()
    const setToken = (newToken = DefaultToken) => {
      token.value = newToken
    }
    const cleanToken = () => {
      token.value = undefined
    }
    return {
      token,
      setToken,
      cleanToken,
    }
  },
  {
    unistorage: true,
  },
)
