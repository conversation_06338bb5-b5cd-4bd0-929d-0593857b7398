import type { DictItem } from '@/types/api/Dict'
import { batchGetDictItems } from '@/api/dict'
import { defineStore } from 'pinia'

interface DictData {
  [key: string]: DictItem[]
}

interface LoadingStatus {
  [key: string]: boolean
}

interface DictMap {
  [key: string]: {
    [key: string]: string | undefined
  }
}

export const useDictStore = defineStore('dict', {
  state: () => ({
    // 使用对象存储不同类型的字典，键为字典类型，值为数组
    dictData: {} as DictData,
    // 存储每种字典的加载状态
    loadingStatus: {} as LoadingStatus,
    // 存储字典的map对象
    dictMap: {} as DictMap,
  }),
  getters: {
    /**
     * 根据类型获取字典数据
     * @param state - Pinia state
     * @returns - 返回一个函数，接收字典类型，返回对应的字典数组
     */
    getDictByType:
      state =>
      (type: string, fieldMap = { label: 'label', value: 'value' }) => {
        const dictItems = state.dictData[type] || []
        return dictItems.map(item => ({
          [fieldMap.label]: item.itemText!,
          [fieldMap.value]: item.itemValue!,
        }))
      },
    /**
     * 根据类型获取字典map数据
     * @param state - Pinia state
     * @returns - 返回一个函数，接收字典类型，返回对应的字典map对象
     */
    getDictMapByType: state => (type: string) => {
      return state.dictMap[type] || {}
    },
    /**
     * 根据类型获取字典项的文本
     * @param state - Pinia state
     * @returns - 返回一个函数，接收字典类型，返回对应的字典map对象
     */
    getDictLabel: state => (type: string, value: string | undefined, placeholder: string = '-') => {
      if (!state.dictMap[type]) return placeholder
      if (value === undefined || value === null) return placeholder
      return state.dictMap[type][value] || placeholder
    },
    /**
     * 检查特定类型的字典是否正在加载
     * @param state - Pinia state
     * @returns - 返回一个函数，接收字典类型，返回加载状态
     */
    isLoading: state => (type: string) => {
      return !!state.loadingStatus[type]
    },
  },
  actions: {
    /**
     * 异步获取并设置指定类型或类型数组的字典数据
     * 如果某个类型的数据已存在或正在加载，则跳过该类型
     * @param types - 需要获取的字典类型或类型数组
     */
    async fetchDict(types: string | string[]) {
      const requestedTypes = Array.isArray(types) ? types : [types] // 统一处理为数组
      const typesToFetch: string[] = []

      // 筛选出需要获取的类型，并立即标记为加载中
      for (const type of requestedTypes) {
        // 检查类型是否有效（非空字符串等）
        if (!type) {
          console.warn('请求的字典类型为空，已跳过')
          continue
        }
        if (!this.dictData[type] && !this.loadingStatus[type]) {
          typesToFetch.push(type)
          this.loadingStatus[type] = true // 标记为加载中
        }
      }

      // 如果没有需要获取的新类型，则直接返回
      if (typesToFetch.length === 0) {
        return
      }

      try {
        // 调用 postDictItemsBatch 接口，传入需要获取的类型数组
        const returnedData = await batchGetDictItems(typesToFetch)

        // 更新成功获取到的字典数据
        for (const type of typesToFetch) {
          // Filter items from the returnedData that match the current type
          const dictItemsForType = returnedData.filter(item => item.dictCode === type)
          this.dictData[type] = dictItemsForType // 更新数据，空数据也接受
          // 将字典数据转成map对象
          this.dictMap[type] = {}
          dictItemsForType.forEach(item => {
            if (item.itemValue !== undefined) {
              this.dictMap[type][item.itemValue] = item.itemText
            }
          })
          this.loadingStatus[type] = false // 标记加载完成
        }
      } catch {
        // 网络请求或代码执行异常
        // const errorMsg = `批量获取字典 [${typesToFetch.join(', ')}] 时网络或程序出错`
        // console.error(`${errorMsg}:`, error)
        // uni.showToast({
        //   title: errorMsg,
        //   icon: 'none',
        //   duration: 3000,
        // })
        // 将所有尝试获取的类型标记为加载完成（异常）并设置为空数组
        for (const type of typesToFetch) {
          this.dictData[type] = []
          this.dictMap[type] = {}
          this.loadingStatus[type] = false
        }
      }
    },

    /**
     * 清空指定类型或所有类型的字典数据
     * @param [type] - 可选，要清空的字典类型。如果省略，则清空所有字典。
     */
    clearDict(type?: string) {
      if (type) {
        delete this.dictData[type]
        delete this.dictMap[type]
        delete this.loadingStatus[type] // 同时清除加载状态
      } else {
        this.dictData = {}
        this.dictMap = {}
        this.loadingStatus = {}
      }
    },

    /**
     * 强制重新获取指定类型的字典数据
     * @param type - 需要重新获取的字典类型
     */
    async forceFetchDict(type: string) {
      // 强制获取单个时，也调用批量接口，保持一致性
      // 先清除旧数据和加载状态
      this.clearDict(type)
      // 再调用 fetchDict 获取
      await this.fetchDict([type])
    },
  },
})
