interface MenuButtonBoundingClientRect {
  width: number
  height: number
  top: number
  left: number
  right: number
  bottom: number
}

export const useAppStore = defineStore(
  'app',
  () => {
    const darkMode = ref(false)
    const statusBarHeight = ref(0)
    const menuButtonBounding = ref<MenuButtonBoundingClientRect>()
    const isPrivacyAgreed = ref(false)

    const customBarHeight = computed(() =>
      !menuButtonBounding.value
        ? 0
        : menuButtonBounding.value.bottom + menuButtonBounding.value.top - statusBarHeight.value,
    )

    // #ifdef H5
    watch(
      darkMode,
      isDark => {
        isDark
          ? document.documentElement.classList.add('dark')
          : document.documentElement.classList.remove('dark')
      },
      {
        immediate: true,
      },
    )
    // #endif

    function setPrivacyAgreed(agreed: boolean) {
      isPrivacyAgreed.value = agreed
    }

    return {
      darkMode,
      statusBarHeight,
      customBarHeight,
      menuButtonBounding,
      isPrivacyAgreed,
      setPrivacyAgreed,
    }
  },
  {
    persist: {
      paths: ['isPrivacyAgreed'],
    },
  },
)
