<svg width="42" height="42" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.92938 26.4232C6.15804 26.7948 6.18663 27.2664 6.02942 27.7095L5.07188 30.9108C4.8575 31.6825 5.51492 32.2542 6.24379 32.0255L9.13071 31.168C9.91675 30.9108 10.5313 31.2395 11.2616 31.6825C13.3482 32.9116 15.9478 33.5405 18.2916 33.5405C25.3803 33.5405 32.5833 28.0667 32.5833 19.2488C32.5833 11.6456 26.4379 5 18.3203 5C10.0168 5 4 11.7742 4 19.2917C4 21.6927 4.70029 24.1651 5.92938 26.4232Z" fill="#BA90FF" fill-opacity="0.2"/>
<g filter="url(#filter0_d_93_36)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M26.6813 19.3058C26.6813 20.3062 25.8667 21.135 24.852 21.135C23.8374 21.135 23.0225 20.3062 23.0225 19.3058C23.0225 18.2912 23.8374 17.4766 24.852 17.4766C25.8667 17.4766 26.6813 18.2912 26.6813 19.3058Z" fill="white" fill-opacity="0.8"/>
</g>
<g filter="url(#filter1_d_93_36)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.0931 19.3058C20.0931 20.3062 19.2785 21.135 18.2638 21.135C17.2492 21.1208 16.4343 20.3062 16.4343 19.2916C16.4343 18.2912 17.2634 17.4621 18.2638 17.4766C19.2785 17.4766 20.0931 18.2912 20.0931 19.3058Z" fill="white" fill-opacity="0.8"/>
</g>
<g filter="url(#filter2_d_93_36)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.5041 19.3058C13.5041 20.3062 12.6895 21.1208 11.6746 21.135C10.6742 21.135 9.8454 20.3062 9.8454 19.3058C9.8454 18.2912 10.66 17.4766 11.6746 17.4766C12.6895 17.4766 13.5041 18.2912 13.5041 19.3058Z" fill="white" fill-opacity="0.8"/>
</g>
<path d="M31.4957 15.0107C35.1656 16.9268 37.5153 20.7923 37.5153 24.9502C37.5152 26.8316 36.9667 28.7696 36.0035 30.5391C35.8246 30.8302 35.8023 31.1998 35.9254 31.5469L36.6754 34.0557C36.8433 34.6604 36.3286 35.1079 35.7574 34.9287L33.4948 34.2568C32.8788 34.0554 32.3971 34.313 31.8248 34.6602C30.1898 35.6233 28.1527 36.1162 26.316 36.1162C23.7256 36.1162 21.116 35.1826 19.068 33.4863C25.7428 32.9048 32.1489 27.5646 32.149 19.249C32.149 17.7873 31.9203 16.3612 31.4957 15.0107Z" fill="url(#paint0_linear_93_36)"/>
<defs>
<filter id="filter0_d_93_36" x="18.0225" y="12.4766" width="23.6588" height="23.6584" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="5" dy="5"/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.576471 0 0 0 0 0.360784 0 0 0 0 0.937255 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_93_36"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_93_36" result="shape"/>
</filter>
<filter id="filter1_d_93_36" x="11.4343" y="12.4764" width="23.6588" height="23.6586" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="5" dy="5"/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.576471 0 0 0 0 0.360784 0 0 0 0 0.937255 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_93_36"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_93_36" result="shape"/>
</filter>
<filter id="filter2_d_93_36" x="4.8454" y="12.4766" width="23.6588" height="23.6584" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="5" dy="5"/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.576471 0 0 0 0 0.360784 0 0 0 0 0.937255 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_93_36"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_93_36" result="shape"/>
</filter>
<linearGradient id="paint0_linear_93_36" x1="22.8668" y1="18.7552" x2="22.8668" y2="41.1216" gradientUnits="userSpaceOnUse">
<stop stop-color="#BC94FF"/>
<stop offset="1" stop-color="#9F66FF"/>
</linearGradient>
</defs>
</svg>
