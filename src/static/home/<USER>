<svg width="375" height="812" viewBox="0 0 375 812" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_53_3)">
<mask id="mask0_53_3" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="17" y="502" width="559" height="526">
<path d="M576 502H17V1028H576V502Z" fill="white"/>
</mask>
<g mask="url(#mask0_53_3)">
<path d="M576 502H17V1028H576V502Z" fill="url(#paint0_radial_53_3)"/>
</g>
<mask id="mask1_53_3" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="375" height="244">
<path d="M375 0H0V244H375V0Z" fill="white"/>
</mask>
<g mask="url(#mask1_53_3)">
<path d="M0 0H375V244H0V0Z" fill="url(#paint1_linear_53_3)"/>
</g>
<mask id="mask2_53_3" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="265" y="278" width="309" height="291">
<path d="M574 278H265V569H574V278Z" fill="white"/>
</mask>
<g mask="url(#mask2_53_3)">
<path d="M574 278H265V569H574V278Z" fill="url(#paint2_radial_53_3)"/>
</g>
<mask id="mask3_53_3" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="-160" y="104" width="353" height="239">
<path d="M193 104H-160V343H193V104Z" fill="white"/>
</mask>
<g mask="url(#mask3_53_3)">
<path d="M193 104H-160V343H193V104Z" fill="url(#paint3_radial_53_3)"/>
</g>
</g>
<defs>
<radialGradient id="paint0_radial_53_3" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(296.5 765) rotate(62.3139) scale(283.686 301.483)">
<stop stop-color="#CADDFF"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint1_linear_53_3" x1="0" y1="0" x2="0" y2="244" gradientUnits="userSpaceOnUse">
<stop stop-color="#4B8DFE"/>
<stop offset="1" stop-color="#F7F7F5" stop-opacity="0.01"/>
</linearGradient>
<radialGradient id="paint2_radial_53_3" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(419.5 423.5) rotate(62.3335) scale(156.916 166.622)">
<stop stop-color="#CADDFF"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint3_radial_53_3" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(16.5 223.5) rotate(53.6075) scale(111.923 165.309)">
<stop stop-color="#CADDFF"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_53_3">
<rect width="375" height="812" fill="white"/>
</clipPath>
</defs>
</svg>
