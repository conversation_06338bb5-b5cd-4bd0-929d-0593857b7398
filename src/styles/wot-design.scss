/* #ifndef APP-NVUE */
:root,
page {
  --wot-color-theme: #37ACFE;
  --wot-color-gray-2: #f2f3f5;
  --wot-input-placeholder-color: #bfbfbf;
  --wot-message-box-width: 80vw;
  --wot-message-box-radius: 12px;
  --wot-message-box-padding: 0px;
  --wot-message-box-bg: #FFF;
  --wot-message-box-title-fs: 18px;
  --wot-message-box-title-color: #2C3E50;
  --wot-message-box-content-fs: 15px;
  --wot-message-box-content-color: #606266;
  --wot-progress-height: 6px;

  image {
    border-radius: 6px;
  }
}

.wd-calendar__confirm {
  .wd-button {
    border-radius: var(--wot-button-large-radius, 8px) !important;
  }
}

.wd-select-picker__footer {
  .wd-button {
    border-radius: var(--wot-button-large-radius, 8px) !important;
  }
}

.wd-progress__label {
  width: max-content!important;
}

.wd-tag {
  padding: 3px 6px!important;
}

.wd-message-box {
  width: var(--wot-message-box-width);
  padding: 0;
  overflow: hidden;
  background-color: var(--wot-message-box-bg);
  border-radius: var(--wot-message-box-radius);
  box-shadow: 0 2px 8px rgb(0 0 0 / 6%);

  .wd-message-box__title {
    box-sizing: border-box;
    padding: 16px;
    font-size: var(--wot-message-box-title-fs);
    font-weight: 500;
    line-height: 1.2;
    color: var(--wot-message-box-title-color);
    text-align: left;
    border-bottom: 1px solid #F2F2F2;
  }

  .wd-message-box__main,
  .wd-message-box__content {
    box-sizing: border-box;
    padding: 36px 24px;
    font-size: var(--wot-message-box-content-fs);
    font-weight: 500;
    color: var(--wot-message-box-content-color);
    text-align: center;
  }

  .wd-message-box__actions {
    display: flex;
    height: 60px;
    padding: 0;
    border-top: 1px solid #F9FAFB;

    .wd-message-box__actions-btn {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      height: 100%;
      ;
      margin: 0;
      border-radius: 0;

      &:first-child {
        background-color: white;
      }
    }
  }
}

/* #endif */
