import pkg from '@/../package.json'
import { pascalCase } from 'change-case'

const ViteMode = import.meta.env.VITE_MODE || 'production'
const PascalCaseViteMode = pascalCase(ViteMode)

export { default as pkg } from '@/../package.json'

// 请求
/** HDS 服务请求基地址 */
export const HDSBaseUrl = import.meta.env.VITE_HDS_BASE_API || ''
/** 商户通服务请求基地址 */
export const MerchantBaseUrl = import.meta.env.VITE_MERCHANT_BASE_API || ''
/** 鉴权地址 */
export const AuthBaseUrl = import.meta.env.VITE_AUTH_BASE_API || ''
export const HDSBaseAuth = import.meta.env.VITE_HDS_BASE_AUTH || ''
export const MerchantBaseAuth = import.meta.env.VITE_MERCHANT_BASE_AUTH || ''

/** 默认请求头 */
export const DefaultHeaders = {
  Accept: 'application/json',
  'Content-Type': 'application/json; charset=utf-8',
  'X-Version': `${pkg.name}/${pkg.version}`,
}

/** 登录态键 */
export const TokenKey = `token${PascalCaseViteMode}`
/** 默认登录态 */
export const DefaultToken = ''

export const ThemeKey = `theme${PascalCaseViteMode}`
