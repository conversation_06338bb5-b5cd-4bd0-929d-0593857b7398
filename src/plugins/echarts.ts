import { LineChart } from 'echarts/charts'
import {
  DataZoomComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
} from 'echarts/components'
import * as echarts from 'echarts/core'
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'

echarts.use([
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  LineChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
  DataZoomComponent,
])

// 基础图表配置
export const baseChartOption = {
  grid: {
    left: 20, // 增加左侧留白，更好显示Y轴标签
    right: 20,
    top: 30, // 增加顶部留白
    bottom: 30, // 增加底部留白，更好显示X轴标签
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    axisLine: {
      lineStyle: {
        color: '#EBEEF5',
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: '#606266',
      margin: 15, // 增加标签与轴线的距离
      fontSize: 12, // 字体大小
      rotate: 0, // 可根据需要设置旋转角度
      formatter(value: string) {
        return value
      },
    },
  },
  yAxis: {
    type: 'value',
    splitLine: {
      lineStyle: {
        color: '#EBEEF5',
      },
    },
    axisLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: '#909399',
    },
  },
  series: [],
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderColor: '#E4E7ED',
    borderWidth: 1,
    textStyle: {
      color: '#303133',
    },
    axisPointer: {
      type: 'line',
      lineStyle: {
        color: '#E4E7ED',
      },
    },
  },
}

// 导出配置好的echarts实例
export { echarts }
