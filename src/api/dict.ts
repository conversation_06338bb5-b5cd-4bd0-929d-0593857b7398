import type { DictItem } from '@/types/api/Dict'
import { getInstance } from '@/service'
import { useUserStore } from '@/store'

/**
 * @summary 批量根据字典编码查询字典项列表
 * @description 获取多个字典编码下的所有字典项
 */
export async function batchGetDictItems(data: string[]): Promise<DictItem[]> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: '/light/operation/dict/items/batch',
    merchant: '/light/operation/dict/items/batch',
  }
  const path = urlMap[userType!]
  return await getInstance().post(path, data)
}

/**
 * @summary 根据字典编码查询字典项列表
 * @description 获取指定字典编码下的所有字典项
 */
export async function getDictItems(dictCode: string): Promise<DictItem[]> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: `/light/operation/dict/items/${dictCode}`,
    merchant: `/light/operation/dict/items/${dictCode}`,
  }
  const path = urlMap[userType!]
  return await getInstance().get(path)
}
