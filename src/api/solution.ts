import type { PaginatedContent } from '@/service/types'
import type {
  Solution,
  SolutionCategory,
  SolutionGetParams,
  SolutionListParams,
  SolutionPageParams,
} from '@/types/api/Solution'
import { getInstance, hdsInstance } from '@/service'
import { useUserStore } from '@/store'

/**
 * @description 获取方案详情
 * @param params SolutionGetParams
 * @returns Promise<Solution>
 */
export async function getSolution(params: SolutionGetParams): Promise<Solution> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    merchant: '/light/operation/solution/get',
    haier: '/light/operation/solution/get',
  }
  const path = urlMap[userType!]
  return await getInstance().get(path, { params })
}

/**
 * @description 方案列表
 * @param params SolutionListParams
 * @returns Promise<Solution>
 */
export async function getSolutionList(params: SolutionListParams): Promise<Solution> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    merchant: '/light/operation/solution/list',
    haier: '/light/operation/solution/list',
  }
  const path = urlMap[userType!]
  return await getInstance().get(path, { params })
}

/**
 * @description 分页查询方案列表
 * @param params SolutionPageParams
 * @returns Promise<PaginatedContent<Solution>>
 */
export async function getSolutionPage(
  params: SolutionPageParams,
): Promise<PaginatedContent<Solution>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    merchant: '/light/operation/solution/page',
    haier: '/light/operation/solution/page',
  }
  const path = urlMap[userType!]
  return await getInstance().get(path, { params })
}

/**
 * @description 获取所有方案分类
 * @returns Promise<SolutionCategory[]>
 */
export async function getSolutionCategoryList(): Promise<SolutionCategory[]> {
  return await hdsInstance.get<SolutionCategory[]>('/light/operation/solution-category/list')
}
