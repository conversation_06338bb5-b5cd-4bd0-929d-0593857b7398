import type { PaginatedContent } from '@/service/types'
import type { Message, MessagePageParams } from '@/types/api/Message'
import { getInstance } from '@/service'
import { useUserStore } from '@/store'

export async function getMessagePage(
  params: MessagePageParams,
): Promise<PaginatedContent<Message>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType || 'haier'
  const urlMap = {
    haier: '/light/operation/message/page',
    merchant: '/light/operation/message/page',
  } as const
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().get(path, { params })
}

export async function getMessageDetail(id: number): Promise<Message> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType || 'haier'
  const urlMap = {
    haier: '/light/operation/message/detail',
    merchant: '/light/operation/message/detail',
  } as const
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().get(path, { params: { id } })
}

export async function markMessageRead(messageId: number): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType || 'haier'
  const urlMap = {
    haier: '/light/operation/message/mark-read',
    merchant: '/light/operation/message/mark-read',
  } as const
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().post(path, undefined, { params: { messageId } })
}

export async function batchMarkMessageRead(data: number[]): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType || 'haier'
  const urlMap = {
    haier: '/light/operation/message/batch-mark-read',
    merchant: '/light/operation/message/batch-mark-read',
  } as const
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().post(path, data)
}

export async function getMessageUnreadCount(): Promise<number> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType || 'haier'
  const urlMap = {
    haier: '/light/operation/message/unread-count',
    merchant: '/light/operation/message/unread-count',
  } as const
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().get(path)
}
