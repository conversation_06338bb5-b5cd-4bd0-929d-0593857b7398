// 获取嵌套对象属性的值，带类型断言
export const getValueByPath = <T = any>(obj: Record<string, any>, path: string): T | undefined => {
  if (!path) return undefined

  // 支持a.b[0].c格式的路径
  const segments = path.replace(/\[(\w+)\]/g, '.$1').split('.')
  let current = obj

  for (let i = 0; i < segments.length; i++) {
    const key = segments[i]
    if (current === undefined || current === null) return undefined
    current = current[key]
  }

  return current as T
}

// 设置嵌套对象属性的值
export const setValueByPath = (obj: Record<string, any>, path: string, value: any) => {
  if (!path) return

  // 支持a.b[0].c格式的路径
  const segments = path.replace(/\[(\w+)\]/g, '.$1').split('.')
  let current = obj

  for (let i = 0; i < segments.length - 1; i++) {
    const key = segments[i]
    // 如果不存在，则创建对象或数组
    if (current[key] === undefined) {
      // 检查下一个key是否是数字，如果是则创建数组，否则创建对象
      const nextKey = segments[i + 1]
      current[key] = /^\d+$/.test(nextKey) ? [] : {}
    }
    current = current[key]
  }

  const lastKey = segments[segments.length - 1]
  current[lastKey] = value
}
