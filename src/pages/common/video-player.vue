<script setup lang="ts">
import { onLoad, onUnload } from '@dcloudio/uni-app'
import { ref } from 'vue'

// 视频信息
const videoUrl = ref('')
const videoTitle = ref('视频播放')
const isFullScreen = ref(false)
const videoContext = ref<UniApp.VideoContext | null>(null)
const isPlaying = ref(false)
const showControls = ref(true)
const progressPercent = ref(0)
const currentTime = ref(0)
const duration = ref(0)
const isLoaded = ref(false)
const isLoading = ref(true)
const error = ref('')

// 格式化时间
const formatTime = (time: number) => {
  const minutes = Math.floor(time / 60)
  const seconds = Math.floor(time % 60)
  return `${minutes < 10 ? `0${minutes}` : minutes}:${seconds < 10 ? `0${seconds}` : seconds}`
}

// 页面加载，获取URL参数
onLoad((options: Record<string, string> = {}) => {
  if (options && options.url) {
    try {
      videoUrl.value = decodeURIComponent(options.url)
    } catch {
      videoUrl.value = options.url
    }
  }

  if (options && options.title) {
    try {
      videoTitle.value = decodeURIComponent(options.title)
      // 设置导航栏标题
      uni.setNavigationBarTitle({
        title: videoTitle.value,
      })
    } catch {
      videoTitle.value = options.title
      // 设置导航栏标题
      uni.setNavigationBarTitle({
        title: videoTitle.value,
      })
    }
  }

  // 获取视频上下文
  setTimeout(() => {
    videoContext.value = uni.createVideoContext('video-player')
  }, 500)
})

// 监听全屏事件
const handleFullScreenChange = (e: any) => {
  isFullScreen.value = e.detail.fullScreen
}

// 视频加载中事件
const handleLoading = () => {
  isLoading.value = true
}

// 视频加载错误
const handleError = (e: any) => {
  isLoading.value = false
  error.value = '视频加载失败'
  console.error('视频加载错误:', e)
  uni.showToast({
    title: '视频加载失败',
    icon: 'none',
  })
}

// 视频可以播放事件
const handleCanPlay = () => {
  isLoading.value = false
  isLoaded.value = true
}

// 视频播放进度更新
const handleTimeUpdate = (e: any) => {
  currentTime.value = e.detail.currentTime
  duration.value = e.detail.duration

  if (duration.value > 0) {
    progressPercent.value = (currentTime.value / duration.value) * 100
  }
}

// 视频数据加载完成
const handleMetaLoaded = (e: any) => {
  duration.value = e.detail.duration
  isLoaded.value = true
  isLoading.value = false
}

// 退出全屏
const exitFullScreen = () => {
  if (videoContext.value) {
    videoContext.value.exitFullScreen()
  }
}

// 页面卸载前退出全屏
onUnload(() => {
  if (isFullScreen.value && videoContext.value) {
    exitFullScreen()
  }
})
</script>

<template>
  <view class="video-player-page">
    <view class="video-container">
      <view v-if="isLoading && !error" class="loading-overlay f-c-c">
        <wd-loading color="#3B82F6" />
        <text class="loading-text">视频加载中...</text>
      </view>

      <view v-if="error" class="error-overlay f-c-c">
        <text class="i-carbon-warning-alt mb-2 text-3xl text-[#ff4d4f]"></text>
        <text class="error-text">{{ error }}</text>
        <wd-button size="small" type="primary" custom-class="mt-3" @click="videoUrl = videoUrl"
          >重试</wd-button
        >
      </view>

      <video
        id="video-player"
        class="video-element"
        :src="videoUrl"
        :controls="showControls"
        :show-center-play-btn="!isPlaying"
        :enable-progress-gesture="true"
        :show-fullscreen-btn="true"
        :show-play-btn="true"
        object-fit="contain"
        @fullscreenchange="handleFullScreenChange"
        @timeupdate="handleTimeUpdate"
        @waiting="handleLoading"
        @error="handleError"
        @loadedmetadata="handleMetaLoaded"
        @canplay="handleCanPlay"
      ></video>
    </view>

    <view v-if="videoUrl" class="video-info">
      <view class="section-title">视频信息</view>

      <view class="info-item">
        <text class="item-label">文件名</text>
        <text class="item-value">{{ videoUrl.split('/').pop() || '未知' }}</text>
      </view>

      <view class="info-item">
        <text class="item-label">时长</text>
        <text class="item-value">{{ formatTime(duration) }}</text>
      </view>

      <view class="info-item">
        <text class="item-label">来源</text>
        <text class="item-value">{{
          videoUrl.indexOf('http') === 0 ? '网络视频' : '本地视频'
        }}</text>
      </view>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "视频播放"
  }
}
</route>

<style lang="scss" scoped>
.video-player-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7f7f7;
}

.video-container {
  position: relative;
  width: 100%;
  height: 225px;
  margin-bottom: 15px;
  background-color: #000;
}

.video-element {
  width: 100%;
  height: 100%;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: rgb(0 0 0 / 70%);
}

.loading-text,
.error-text {
  margin-top: 10px;
  font-size: 14px;
  color: #fff;
}

.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 5;
  width: 100%;
  padding: 8px 12px;
  background-color: rgb(0 0 0 / 50%);
}

.progress-bar {
  position: relative;
  height: 4px;
  margin-bottom: 5px;
}

.progress-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgb(255 255 255 / 30%);
  border-radius: 2px;
}

.progress-current {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #3b82f6;
  border-radius: 2px;
}

.time-display {
  font-size: 12px;
  color: #fff;
  text-align: right;
}

.video-info {
  padding: 15px;
  margin: 0 12px;
  background-color: #fff;
  border-radius: 8px;
}

.section-title {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.item-label {
  font-size: 14px;
  color: #666;
}

.item-value {
  max-width: 70%;
  font-size: 14px;
  color: #333;
  text-align: right;
  word-break: break-all;
}
</style>
