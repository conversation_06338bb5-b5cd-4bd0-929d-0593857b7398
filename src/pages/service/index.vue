<script setup lang="ts">
import type { ChatMessage as ServiceChatMessage } from '@/types/api/Service'
import { getChatHistoryPage, getChatTemplates, sendMessage } from '@/api/service'
import { navigateBack, navigateTo } from '@uni-helper/uni-promises'
import { nextTick, ref } from 'vue'

interface ChatMessage extends ServiceChatMessage {
  isAnswering?: boolean
}

const paging = ref<ZPagingInstance>()

const suggestedTopics = ref(['每日热门', '文案生成', '电站新闻'])

const dataList = ref<ChatMessage[]>([])
const isLoadingAnswer = ref(false)
const currentMessage = ref('')
const conversationId = ref<string | undefined>(undefined)
const firstLoaded = ref(false)

async function queryChatHistory(pageNum: number, pageSize: number) {
  try {
    const res = await getChatHistoryPage({
      pageNum,
      pageSize,
    })

    paging.value?.completeByTotal(
      res.content.map(item => ({ ...item, key: item.messageId })).reverse(),
      res.totalElements,
    )
    if (!firstLoaded.value) {
      firstLoaded.value = true
    }
  } catch (error) {
    console.error('Failed to fetch chat history:', error)
    paging.value?.complete(false)
  }
}

async function handleSendMessage(messageText?: string) {
  if (isLoadingAnswer.value) return
  const text = messageText || currentMessage.value
  if (!text.trim()) return

  const question = {
    key: `user-${Date.now()}`,
    messageId: '',
    messageType: null,
    userMessage: text,
    templates: [],
    isAnswering: true,
    aiMessage: '',
  }

  paging.value?.addChatRecordData(question, true)

  currentMessage.value = ''

  isLoadingAnswer.value = true

  try {
    const response = await sendMessage({
      message: text,
      conversationId: conversationId.value,
    })

    if (response.answer) {
      question.aiMessage = response.answer
      question.isAnswering = false
    }
    if (response.conversation_id) {
      conversationId.value = response.conversation_id
    }
    question.messageId = response.message_id!
  } catch (error) {
    console.error('Failed to send message:', error)
    question.isAnswering = false
    question.aiMessage = '抱歉，服务出错了，请稍后再试。'
  } finally {
    isLoadingAnswer.value = false
    question.isAnswering = false
    nextTick(() => {
      paging.value?.scrollToBottom()
    })
  }
}

function handleQuestionClick(question: string) {
  if (isLoadingAnswer.value) return
  handleSendMessage(question)
}

async function handleRefreshTemplates(messageId: string) {
  const templateIndex = dataList.value.findIndex(m => m.messageId === messageId)
  if (templateIndex !== -1) {
    const res = await getChatTemplates()
    dataList.value[templateIndex].templates = res
  }
}

function handleClose() {
  navigateBack({ delta: 1 }).catch(() => {
    navigateTo({ url: '/pages/index/index' })
  })
}
</script>

<template>
  <view class="service-page">
    <view class="service-page__header">
      <view class="header-title">
        <text class="main-title">光小智</text>
        <text class="sub-title">海尔新能源AI客服</text>
      </view>
      <view class="close-button" @click="handleClose">
        <text class="close-icon">✕</text>
      </view>
    </view>

    <z-paging
      ref="paging"
      v-model="dataList"
      class="service-page__content"
      use-chat-record-mode
      :auto-adjust-position-when-chat="false"
      @query="queryChatHistory"
    >
      <view
        v-for="(message, index) in dataList"
        :key="message.key"
        class="chat-message"
        :class="[`chat-message--${message.messageType}`]"
      >
        <view
          v-if="message.messageType === null && message.userMessage"
          class="message-bubble message-bubble--user"
        >
          <text class="message-text">{{ message.userMessage }}</text>
        </view>
        <view
          v-if="message.messageType === null && (message.aiMessage || message.isAnswering)"
          class="message-bubble message-bubble--assistant"
        >
          <view class="message-text">
            <view v-if="message.isAnswering" class="typing-dots">
              <view class="dot" />
              <view class="dot" />
              <view class="dot" />
            </view>
            <text v-else>
              {{ message.aiMessage }}
            </text>
          </view>
          <view
            v-if="message.aiMessage && isLoadingAnswer === false"
            class="message-actions"
            :class="[{ 'message-actions--disabled': isLoadingAnswer }]"
            @click="handleSendMessage(message.userMessage)"
          >
            <view class="action-button retry-button">
              <text class="action-icon">↺</text>
              <text class="action-text">重新回答</text>
            </view>
          </view>
        </view>
        <view v-if="message.messageType === 'template'" class="template-container">
          <view class="prompt-section">
            <text class="prompt-title">Hi，试着问我</text>
            <view class="question-list">
              <view
                v-for="template in message.templates"
                :key="template.id"
                class="question-item"
                :class="[{ 'question-item--disabled': isLoadingAnswer }]"
                @click="handleQuestionClick(template.question!)"
              >
                <text class="question-text">{{ template.question }}</text>
                <view class="send-icon-container">
                  <image src="/static/service/sending.webp" class="send-icon" mode="aspectFit" />
                </view>
              </view>
            </view>
          </view>

          <view v-if="index === 0" class="refresh-section">
            <view
              class="refresh-button"
              :class="[{ 'refresh-button--disabled': isLoadingAnswer }]"
              @click="handleRefreshTemplates(message.messageId!)"
            >
              <text class="refresh-icon">↻</text>
              <text class="refresh-text">换一换</text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
    <view class="service-page__footer">
      <scroll-view scroll-x class="suggested-topics">
        <view
          v-for="(topic, index) in suggestedTopics"
          :key="index"
          class="topic-button"
          :class="[{ 'topic-button--disabled': isLoadingAnswer }]"
          @click="handleSendMessage(topic)"
        >
          <text class="topic-text">{{ topic }}</text>
        </view>
      </scroll-view>
      <view class="input-area">
        <input
          v-model="currentMessage"
          type="text"
          :placeholder="isLoadingAnswer ? '正在回复中...' : '有问题尽管问我'"
          class="input-field"
          placeholder-class="input-placeholder"
          :disabled="isLoadingAnswer"
          @confirm="handleSendMessage()"
        />
        <view
          class="send-button-main"
          :class="[{ 'send-button-main--disabled': isLoadingAnswer }]"
          @click="handleSendMessage()"
        >
          <image src="/static/service/send.svg" class="send-icon-main" mode="aspectFit" />
        </view>
      </view>
      <image src="/static/service/service-logo.webp" class="topic-icon" mode="aspectFit" />
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "智能客服"
  }
}
</route>

<style scoped lang="scss">
@keyframes typing {
  0% {
    opacity: 0.3;
    transform: translateY(0);
  }

  20% {
    opacity: 1;
    transform: translateY(-4px);
  }

  40%,
  100% {
    opacity: 0.3;
    transform: translateY(0);
  }
}

.service-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(147deg, #e3edff, #fafcff 41%);

  &__header {
    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    padding-top: var(--status-bar-height);

    .header-title {
      display: flex;
      flex-direction: column;
    }

    .main-title {
      font-family: 'Alimama FangYuanTi VF', sans-serif;
      font-size: 20px;
      font-weight: 500;
      color: #3d3d3d;
    }

    .sub-title {
      font-family: 'PingFang SC', sans-serif;
      font-size: 7px;
      color: #3d3d3d;
    }

    .close-button {
      padding: 8px;
    }

    .close-icon {
      font-size: 18px;
      color: #333;
    }
  }

  &__content {
    position: relative;
    box-sizing: border-box;
    flex: 1;
    height: 0;

    :deep(.zp-scroll-view) {
      box-sizing: border-box;
      padding: 0 20px;
    }
  }

  .prompt-section {
    padding: 17px 20px;
    margin-top: 10px;
    background-color: #fff;
    border-radius: 18px;
    box-shadow: 0 0 8px 0 rgb(64 150 254 / 3%);
  }

  .prompt-title {
    display: block;
    margin-bottom: 12px;
    font-family: 'PingFang SC', sans-serif;
    font-size: 20px;
    font-weight: bold;
    color: #3d3d3d;
  }

  .question-list {
    display: flex;
    flex-direction: column;
  }

  .question-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #f2f2f2;
    transition: opacity 0.2s;

    &:last-child {
      border-bottom: none;
    }

    &--disabled {
      pointer-events: none;
      opacity: 0.5;
    }
  }

  .question-text {
    flex: 1;
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    color: #3d3d3d;
  }

  .send-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 27px;
    height: 27px;
    margin-left: 10px;
    background-color: #eaf3ff;
    border-radius: 4px;
  }

  .send-icon {
    width: 18px;
    height: 18px;
  }

  .refresh-section {
    display: flex;
    justify-content: flex-start;
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .refresh-button {
    display: flex;
    align-items: center;
    padding: 3px 8px;
    background-color: #fff;
    border-radius: 9px;
    box-shadow: 0 0 8px 0 rgb(64 150 254 / 3%);
    transition: opacity 0.2s;

    &--disabled {
      pointer-events: none;
      opacity: 0.5;
    }
  }

  .refresh-icon {
    margin-right: 4px;
    font-size: 14px;
    color: #a8abb2;
  }

  .refresh-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 11px;
    color: #a8abb2;
  }

  .template-container {
    width: 100%;
  }

  .action-button {
    display: flex;
    align-items: center;
    padding: 3px 8px;
    background-color: #fff;
    border-radius: 9px;
    box-shadow: 0 0 8px 0 rgb(64 150 254 / 3%);
  }

  .action-icon {
    font-size: 14px;
    color: #19385d;
  }

  .action-text {
    margin-left: 4px;
    font-family: 'PingFang SC', sans-serif;
    font-size: 11px;
    color: #19385d;
  }

  .feedback-buttons {
    .action-icon {
      padding: 0 5px;
    }

    .separator {
      width: 1px;
      height: 14px;
      margin: 0 5px;
      background-color: #d8d8d8;
    }
  }

  .chat-message {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;
    margin-top: 12px;
    transform: scaleY(-1);
  }

  .message-bubble {
    position: relative;
    max-width: 80%;
    padding: 10px 15px;
    border-radius: 18px;
    box-shadow: 0 2px 5px rgb(0 0 0 / 5%);

    &--user {
      align-self: flex-end;
      color: #19385d;
      background: linear-gradient(106deg, rgb(182 232 255 / 80%), rgb(181 214 255 / 93%) 97%);
      border-radius: 13px 13px 0;

      .message-text {
        font-weight: 500;
        line-height: 1;
      }
    }

    &--assistant {
      align-self: flex-start;
      color: #19385d;
      background-color: #fff;
      border-radius: 18px 18px 18px 5px;
    }
  }

  .typing-dots {
    display: flex;
    align-items: center;
    padding-top: 8px;

    .dot {
      width: 6px;
      height: 6px;
      margin: 0 2px;
      background-color: #a8abb2;
      border-radius: 50%;
      animation: typing 1.4s infinite both;

      &:nth-child(2) {
        animation-delay: 0.2s;
      }

      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }

  .message-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    line-height: 1.6;
    word-wrap: break-word;
    white-space: pre-wrap;
  }

  .message-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: flex-start;
    padding-top: 8px;
    margin-top: 10px;
    border-top: 1px solid #eaeaea;
    transition: opacity 0.2s;

    &--disabled {
      pointer-events: none;
      opacity: 0.5;
    }

    .action-button {
      padding: 2px 6px;
      background-color: transparent;
      box-shadow: none;
    }

    .action-icon {
      font-size: 13px;
    }

    .action-text {
      font-size: 10px;
    }

    .feedback-buttons .separator {
      background-color: #d1d1d1;
    }
  }

  &__footer {
    position: relative;
    z-index: 10;
    padding: 10px 20px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 12px);
    background: linear-gradient(176deg, #eaf3ff, #fff 98%);
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    box-shadow: 0 -2px 8px rgb(0 0 0 / 6%);
  }

  .suggested-topics {
    box-sizing: border-box;
    padding-right: 20px;
    margin-bottom: 10px;
    margin-left: 80px;
    white-space: nowrap;
  }

  .topic-button {
    display: inline-block;
    padding: 4px 12px;
    margin-right: 10px;
    vertical-align: middle;
    background-color: #fff;
    border-radius: 15px;
    box-shadow: 0 0 8px 0 rgb(64 150 254 / 3%);
    transition: opacity 0.2s;

    &:last-child {
      margin-right: 0;
    }

    &--disabled {
      pointer-events: none;
      opacity: 0.5;
    }
  }

  .topic-icon {
    position: absolute;
    bottom: 42px;
    left: 24px;
    z-index: -1;
    width: 72px;
    height: 64px;
    margin-right: 6px;
  }

  .topic-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    font-weight: 500;
    color: #4b4b4b;
  }

  .input-area {
    display: flex;
    align-items: center;
    padding: 5px 5px 5px 15px;
    background-color: #fff;
    border-radius: 25px;
    box-shadow: 0 0 10px 0 rgb(64 150 254 / 6%);
  }

  .input-field {
    flex: 1;
    height: 30px;
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    line-height: 30px;
    color: #3d3d3d;
  }

  .input-placeholder {
    color: #a8abb2;
  }

  .send-button-main {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    margin-left: 10px;
    border-radius: 50%;
    transition: opacity 0.2s;

    &--disabled {
      pointer-events: none;
      opacity: 0.5;
    }
  }

  .send-icon-main {
    width: 21px;
    height: 21px;
  }
}
</style>
