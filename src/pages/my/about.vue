<script setup lang="ts">
import { useToast } from 'wot-design-uni'

const toast = useToast()

function goToServiceAgreement() {
  toast.info('查看服务协议')
}

function goToPrivacyPolicy() {
  toast.info('查看隐私政策')
}
</script>

<template>
  <div class="about-page">
    <wd-cell-group border>
      <wd-cell title="服务协议" is-link custom-class="about-item" @click="goToServiceAgreement" />
      <wd-cell title="隐私政策" is-link custom-class="about-item" @click="goToPrivacyPolicy" />
    </wd-cell-group>
  </div>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "关于我们"
  }
}
</route>

<style scoped lang="scss">
.about-page {
  min-height: 100vh;
  background-color: white;
}

:deep(.about-item) {
  .wd-cell__title {
    font-size: 15px;
    color: #303133;
  }

  .wd-cell__arrow {
    color: #c0c4cc;
  }
}
</style>
