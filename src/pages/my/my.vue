<script lang="ts" setup>
import { useAuthStore, useUserStore } from '@/store'
import { navigateTo } from '@uni-helper/uni-promises'
import { ref } from 'vue'
import { useMessage } from 'wot-design-uni'

const userStore = useUserStore()
const userInfo = userStore.userInfo
const auth = useAuthStore()
const message = useMessage()

const commonFunctions = ref([
  { name: '个人信息', icon: '/static/user/info.webp', url: '/pages/user/info', isImage: true },
  { name: '修改密码', icon: '/static/user/edit.webp', url: '/pages/user/modify', isImage: true },
  { name: '设置', icon: '/static/user/setting.webp', url: '/pages/setting/index', isImage: true },
])

const moreServices = ref([
  { name: '智能客服', icon: '/static/home/<USER>', url: '/pages/service/index', isImage: true },
  {
    name: '模拟考试',
    icon: '/static/home/<USER>',
    url: '/pages/training-center/index',
    isImage: true,
  },
])

const goToPage = (url: string) => {
  navigateTo({
    url,
  })
}

function handleLogoutAccount() {
  message
    .confirm({
      title: '提示',
      msg: '确定要注销账号吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })
    .then(() => {
      // 注销账号的逻辑可以在这里实现
      // 例如调用注销接口，然后清除本地存储的信息
      auth.cleanToken()
      uni.reLaunch({ url: '/pages/user/login' })
    })
}

function handleLogout() {
  message
    .confirm({
      title: '提示',
      msg: '确定要退出登录吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })
    .then(() => {
      auth.cleanToken()
      uni.reLaunch({ url: '/pages/user/login' })
    })
}

function goAboutUs() {
  navigateTo({
    url: '/pages/my/about',
  })
}
</script>

<template>
  <view class="my-page min-h-screen">
    <view class="header relative">
      <view class="header__gradient-bg absolute inset-0" />
      <view
        class="header__title absolute z-2 w-full pt-12 text-center text-lg text-white font-medium"
      >
        我的
      </view>
      <view class="header__content relative z-1 flex items-center px-4 pb-4 pt-[98px]">
        <image src="/static/logo.png" class="header__avatar mr-4 h-[50px] w-[50px]" />
        <view class="header__info">
          <text class="header__name block">{{ userInfo?.name }}</text>
          <text class="header__signature mt-1 block">{{ userInfo?.phone }}</text>
        </view>
      </view>
    </view>

    <view class="content-section absolute left-0 right-0 top-[170px] z-2 px-4">
      <view class="card common-functions-card card-shadow mb-4 rounded-lg bg-white p-[15px]">
        <text class="card__title mb-4 block text-sm text-[#4B4B4B] font-medium">常用功能</text>
        <view class="card__grid grid grid-cols-4 gap-4">
          <view
            v-for="item in commonFunctions"
            :key="item.name"
            class="card__grid-item flex flex-col items-center"
            @click="goToPage(item.url)"
          >
            <image v-if="item.isImage" class="card__icon mb-1" :src="item.icon" />
            <text class="card__text text-xs">{{ item.name }}</text>
          </view>
        </view>
      </view>

      <view class="card more-services-card card-shadow mb-4 rounded-lg bg-white p-[15px]">
        <text class="card__title mb-4 block text-sm text-[#4B4B4B] font-medium">更多服务</text>
        <view class="card__grid grid grid-cols-4 gap-4">
          <view
            v-for="item in moreServices"
            :key="item.name"
            class="card__grid-item flex flex-col items-center"
            @click="goToPage(item.url)"
          >
            <image v-if="item.isImage" class="card__icon mb-1" :src="item.icon" />
            <text class="card__text text-xs">{{ item.name }}</text>
          </view>
        </view>
      </view>

      <view class="action-list card-shadow overflow-hidden rounded-lg bg-white">
        <wd-cell-group border>
          <wd-cell title="关于我们" is-link @click="goAboutUs"> </wd-cell>
          <wd-cell title="注销账号" is-link @click="handleLogoutAccount"> </wd-cell>
          <wd-cell title="退出登录" is-link @click="handleLogout"> </wd-cell>
        </wd-cell-group>
      </view>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "我的"
  },
  "usingComponents": {
    "wd-cell": "wot-design-uni/components/wd-cell/wd-cell",
    "wd-cell-group": "wot-design-uni/components/wd-cell-group/wd-cell-group",
    "wd-icon": "wot-design-uni/components/wd-icon/wd-icon"
  }
}
</route>

<style scoped lang="scss">
.my-page {
  background-color: #f5f7fa;
}

.header {
  color: white;

  &__gradient-bg {
    height: 283px;
    background: linear-gradient(180deg, #4b8dfe 0%, #f5f7fa 100%);
  }

  &__avatar {
    border-color: #f7f7f5;
    border-width: 2px;
    border-radius: 9999px;
  }

  &__name {
    @apply text-lg font-medium;
  }

  &__signature {
    @apply text-xs mt-1;

    color: white;
  }
}

.card {
  &.card-shadow {
    box-shadow: 5px 5px 10px 5px rgb(0 0 0 / 5%);
  }

  &__icon {
    width: 32px;
    height: 32px;
  }

  &__grid-item {
    cursor: pointer;

    &:active {
      opacity: 0.7;
    }
  }

  &__text {
    color: #4b4b4b;
  }
}

.action-list {
  &.card-shadow {
    box-shadow: 5px 5px 10px 5px rgb(0 0 0 / 5%);
  }

  .action-list__icon {
    @apply mr-2 text-lg;

    color: #4b4b4b;
  }

  :deep(.wd-cell__title) {
    font-size: 14px;
    line-height: 2;
    color: #4b4b4b;
  }

  :deep(.wd-cell) {
    padding: 0 16px;
  }
}
</style>
