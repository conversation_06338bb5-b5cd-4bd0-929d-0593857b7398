<script setup>
defineProps({
  task: {
    type: Object,
    default: () => ({}),
  },
  photos: {
    type: Array,
    default: () => [],
  },
})
</script>

<template>
  <view class="card m-3 rounded-lg bg-white p-4 shadow-sm">
    <view class="card-title mb-3 flex items-center">
      <view class="accent-bar mr-2 h-4 w-1 bg-primary"></view>
      <text class="text-sm text-[#4B4B4B] font-bold">任务信息</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">任务编码</text>
      <text class="value">{{ task?.orderCode || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">巡检计划</text>
      <text class="value">{{ task?.planName || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">开始时间</text>
      <text class="value">{{ task?.startDate || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">结束时间</text>
      <text class="value">{{ task?.endDate || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">是否逾期</text>
      <text class="value">{{ task?.overTime ? '是' : '否' }}</text>
    </view>
  </view>
</template>

<style scoped>
.detail-row {
  display: flex;
  margin-bottom: 10px;
}

.label {
  width: 88px;
  font-size: 14px;
  color: #999;
}

.value {
  flex: 1;
  font-size: 14px;
  color: #333;
}
</style>
