<script setup lang="ts">
import type { InspectionWorkOrder, InspectWorkOrderPageParams } from '@/types/api/Inspect'
import type { SegmentedOption } from 'wot-design-uni/components/wd-segmented/types'
import { getInspectionWorkOrderPage } from '@/api/inspect'
import SearchBar from '@/components/SearchBar.vue'
import { useUserStore } from '@/store'
import { nextTick, ref, watch } from 'vue'
import FilterPopup from './components/FilterPopup.vue'

const keyword = ref('')
const activeTab = ref<string | number>('')
const showFilterPopup = ref(false)
const currentFilters = ref({})
const { options } = usePageExtend()

const tabs = ref<SegmentedOption[]>([
  {
    value: 'TO_PROCESS',
    payload: {
      label: '待处理',
      badge: 0,
    },
  },
  {
    value: 'HANDLED',
    payload: {
      label: '已处理',
      badge: 0,
    },
  },
])

const paging = ref<any>()
const orderList = ref<InspectionWorkOrder[]>([])
const pageSize = ref(20)

const handleTaskUpdated = (data: {
  orderCode?: string
  updatedTask?: InspectionWorkOrder
  needsRefresh?: boolean
}) => {
  if (data.needsRefresh) {
    if (paging.value) {
      paging.value.reload()
    }
    return
  }

  if (!data.orderCode || !data.updatedTask) {
    return
  }

  const { orderCode } = data
  const index = orderList.value.findIndex(
    (item: InspectionWorkOrder) => item.orderCode === orderCode,
  )

  if (index !== -1) {
    if (orderList.value.length === 1 && paging.value) {
      paging.value.reload()
    } else {
      orderList.value.splice(index, 1)
    }
  }
}

const onSearch = () => {
  paging.value?.reload()
}

const queryList = async (pageNum: number, pageSize: number) => {
  const params: InspectWorkOrderPageParams = {
    pageNum,
    pageSize,
  }

  if (activeTab.value === 'HANDLED') {
    params.orderStatus = undefined
  } else if (activeTab.value !== 'ALL') {
    params.orderStatus = activeTab.value as string
  }

  // 合并筛选条件
  Object.assign(params, currentFilters.value)

  if (keyword.value) {
    if (/^\d*$/.test(keyword.value)) {
      params.stationCode = keyword.value
    } else {
      params.stationName = keyword.value
    }
  }

  if (options.planId) {
    params.planId = options.planId
  }

  if (options.opName) {
    params.opName = options.opName
  }

  if (options.specialFlag) {
    params.specialFlag = options.specialFlag
  }

  try {
    const res = await getInspectionWorkOrderPage({
      ...params,
      orderStatus: activeTab.value as string,
    })
    paging.value?.completeByTotal(res.content, res.totalElements)
  } catch {
    paging.value?.complete(false)
  }
}

const userStore = useUserStore()

const goToDetail = (order: InspectionWorkOrder) => {
  let action = 'handle'
  const userType = userStore.userInfo?.userType
  if (activeTab.value === 'HANDLED' || userType === 'haier') {
    action = 'view'
  }
  uni.navigateTo({
    url: `/pages/inspect/task/index?orderCode=${order.orderCode}&action=${action}`,
    events: {
      taskUpdated: handleTaskUpdated,
    },
  })
}

const handleApplyFilter = (filters: any) => {
  currentFilters.value = filters
  showFilterPopup.value = false
  paging.value?.reload()
}

watch(activeTab, async () => {
  if (paging.value) {
    orderList.value = []
    await nextTick()
  }
  paging.value?.reload()
})
</script>

<template>
  <view class="work-order-list-page">
    <!-- 搜索区域 -->
    <SearchBar
      v-model="keyword"
      :show-filter="true"
      placeholder="请输入电站编号/电站名称"
      @search="onSearch"
      @filter="showFilterPopup = true"
    />

    <!-- Tab 切换 -->
    <view class="segmented-container">
      <wd-segmented v-model:value="activeTab" :options="tabs">
        <template #label="{ option }">
          <view class="segmented-item-content">
            <text>{{ option.payload.label }}</text>
            <wd-badge
              v-if="option.payload.badge"
              :model-value="option.payload.badge"
              custom-class="segmented-item-badge"
              type="danger"
            />
          </view>
        </template>
      </wd-segmented>
    </view>

    <!-- 任务列表 -->
    <z-paging
      ref="paging"
      v-model="orderList"
      class="order-list-paging"
      :fixed="false"
      :default-page-size="pageSize"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
      @query="queryList"
    >
      <view class="order-list-content">
        <view
          v-for="order in orderList"
          :key="order.id"
          class="order-card"
          @click="goToDetail(order)"
        >
          <view class="card-header">
            <text class="title">{{ order.stationCode }}</text>
          </view>
          <view class="card-body">
            <view class="detail-item">
              <text class="label">巡检计划</text>
              <text class="value">{{ order.planName }}</text>
            </view>
            <view class="detail-item">
              <text class="label">开始时间</text>
              <text class="value">{{ order.startDate }}</text>
            </view>
            <view class="detail-item">
              <text class="label">结束时间</text>
              <text class="value">{{ order.endDate }}</text>
            </view>
            <view class="detail-item">
              <text class="label">电站名称</text>
              <text class="value">{{ order.stationName }}</text>
            </view>
            <view class="detail-item">
              <text class="label">联系方式</text>
              <text class="value">{{ order.stationPhone }}</text>
            </view>
            <view class="detail-item">
              <text class="label">电站地址</text>
              <text class="value">{{ order.address }}</text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>

    <FilterPopup
      v-model:model-value="showFilterPopup"
      v-model:filter="currentFilters"
      @apply="handleApplyFilter"
    />
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "任务列表"
  }
}
</route>

<style scoped lang="scss">
.work-order-list-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .segmented-container {
    padding: 8px 12px;

    --wot-segmented-item-bg-color: #fff;

    :deep(.wd-segmented__item.is-active) {
      color: white;
      background-color: $uni-color-primary;
    }

    :deep(.wd-segmented__item) {
      min-width: none;
      color: #91929e;
    }

    :deep(.wd-segmented) {
      width: 100%;
    }
  }

  .segmented-item-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    .segmented-item-badge {
      position: absolute;
      top: 2px;
      right: -5px;
      transform: translate(50%, -50%) scale(0.8);
    }
  }

  .order-list-paging {
    flex: 1;
  }

  .order-list-content {
    padding: 8px 12px;
  }

  .order-card {
    padding: 12px;
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 5%);

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 8px;
      margin-bottom: 10px;
      border-bottom: 1px solid #f0f0f0;

      .title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }
    }

    .card-body {
      .detail-item {
        display: flex;
        margin-bottom: 4px;
        font-size: 14px;
        line-height: 1.6;

        .label {
          flex-shrink: 0;
          width: 70px;
          margin-right: 8px;
          color: #666;
        }

        .value {
          color: #333;
          word-break: break-all;
        }
      }
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
