<script setup lang="ts">
import type {
  InspectWorkOrderPageParams,
  InspectWorkOrderStatisticsParams,
} from '@/types/api/Inspect'
import { getInspectionPlanList } from '@/api/inspect'
import { usePageExtend } from '@/composables/usePageExtend'
import { useRegion } from '@/composables/useRegion'
import { pmSpecialFlag, property } from '@/constants/dict'
import { useUserStore } from '@/store'
import { useDictStore } from '@/store/modules/dict'
import { computed, onMounted, reactive, ref } from 'vue'

interface FilterData extends InspectWorkOrderPageParams {}

const props = defineProps<{ stationType?: string; showOpCondition?: boolean }>()

const emit = defineEmits<{
  (e: 'apply', filters: FilterData): void
}>()

const internalShow = defineModel<boolean>('modelValue')

const dictStore = useDictStore()
const userStore = useUserStore()
const userInfo = userStore.userInfo
const { options } = usePageExtend()

const {
  provinceOptions,
  cityOptions,
  regionOptions,
  provinceMap,
  cityMap,
  regionMap,
  setCity,
  setRegion,
} = useRegion()

const planOptions = ref<any[]>([])

onMounted(() => {
  dictStore.fetchDict(['inspection_type', 'station_type'])
  if (options.inspectionType) handleInspectionTypeChange(options.inspectionType)
})

const filters = reactive<InspectWorkOrderStatisticsParams>({
  inspectionType: '',
  planId: undefined,
  // stationType: '',
  specialFlag: '',
  opName: '',
  provinceId: undefined,
  cityId: undefined,
  regionId: undefined,
  progressMin: undefined,
  progressMax: undefined,
})

const inspectionTypeOptions = computed(() => dictStore.getDictByType('inspection_type') || [])

const handleReset = () => {
  filters.inspectionType = ''
  filters.planId = undefined
  // filters.stationType = ''
  filters.specialFlag = ''
  filters.opName = ''
  filters.provinceId = undefined
  filters.cityId = undefined
  filters.regionId = undefined
  filters.progressMin = undefined
  filters.progressMax = undefined
  planMap.value = {}
  cityMap.value = {}
  regionMap.value = {}
  provinceMap.value = {}
  cityOptions.value = []
  regionOptions.value = []
}

const handleClose = () => {
  internalShow.value = false
}

const handleApply = () => {
  emit('apply', { ...filters })
  internalShow.value = false
}

// const handleStationTypeChange = (val: any) => {
//   if (val.value) {
//     getInspectionPlanList({
//       stationType: val.value,
//       inspectionType: filters.inspectionType,
//     }).then(res => {
//       planOptions.value = res.map(item => {
//         return {
//           label: item.planName,
//           value: item.id,
//         }
//       })
//       planMap.value = res.reduce((acc: any, item) => {
//         acc[item.id!.toString()] = item.planName
//         return acc
//       }, {})
//       filters.planId = undefined
//     })
//   }
// }

const planMap = ref<Record<string, string>>({})

const handleInspectionTypeChange = (val: any) => {
  if (val.value && props.stationType) {
    getInspectionPlanList({
      stationType: props.stationType,
      inspectionType: val.value,
    }).then(res => {
      planOptions.value = res.map(item => {
        return {
          label: item.planName,
          value: item.id,
        }
      })
      planMap.value = res.reduce((acc: any, item) => {
        acc[item.id!.toString()] = item.planName
        return acc
      }, {})
      filters.planId = undefined
    })
  }
}

const handleProviceConfirm = (val: any) => {
  setCity(val.value)
  filters.cityId = undefined
  filters.regionId = undefined
}

const handleCityConfirm = (val: any) => {
  setRegion(val.value)
  filters.regionId = undefined
}
</script>

<template>
  <wd-popup v-model="internalShow" position="bottom" custom-style="height: 72vh">
    <view class="filter-popup">
      <view class="filter-popup__header">
        <text class="filter-popup__title">筛选</text>
        <view class="filter-popup__close" @click="handleClose">
          <text class="i-carbon-close filter-popup__close-icon"></text>
          <text>关闭</text>
        </view>
      </view>

      <view scroll-y class="filter-popup__content">
        <!-- <view class="filter-section">
          <text class="filter-section__title">电站类型</text>
          <wd-radio-group
            v-model="filters.stationType"
            shape="dot"
            inline
            @change="handleStationTypeChange"
          >
            <wd-radio
              v-for="item in stationTypeOptions"
              :key="item.value"
              :value="item.value"
              custom-class="filter-radio"
            >
              {{ item.label }}
            </wd-radio>
          </wd-radio-group>
        </view> -->
        <template v-if="!options.inspectionType && !options.planId">
          <view class="filter-section">
            <text class="filter-section__title">巡检类型</text>
            <wd-radio-group
              v-model="filters.inspectionType"
              shape="dot"
              inline
              @change="handleInspectionTypeChange"
            >
              <wd-radio
                v-for="item in inspectionTypeOptions"
                :key="item.value"
                :value="item.value"
                custom-class="filter-radio"
              >
                {{ item.label }}
              </wd-radio>
            </wd-radio-group>
          </view>
          <view class="filter-section">
            <text class="filter-section__title">巡检计划</text>
            <wd-select-picker
              v-model="filters.planId!"
              use-default-slot
              type="radio"
              :columns="planOptions"
              custom-class="filter-section__picker"
            >
              <text
                class="picker__text"
                :class="[!filters.planId && 'picker__text--placeholder']"
                >{{ planMap[filters.planId || ''] || '请先选择巡检类型' }}</text
              >
              <text class="i-carbon-chevron-down picker__icon"></text>
            </wd-select-picker>
          </view>
        </template>
        <template v-if="userInfo?.userType === 'haier'">
          <view class="filter-section">
            <text class="filter-section__title">资方</text>
            <wd-select-picker
              v-model="filters.specialFlag!"
              use-default-slot
              type="radio"
              :columns="property"
              custom-class="filter-section__picker"
            >
              <text
                class="picker__text"
                :class="[!filters.specialFlag && 'picker__text--placeholder']"
                >{{ pmSpecialFlag[filters.specialFlag || ''] || '选择资方' }}</text
              >
              <text class="i-carbon-chevron-down picker__icon"></text>
            </wd-select-picker>
          </view>
          <view v-if="showOpCondition !== false" class="filter-section">
            <text class="filter-section__title">运维商</text>
            <wd-input
              v-model="filters.opName"
              placeholder="请输入运维商"
              clearable
              custom-class="filter-section__input"
            />
          </view>
        </template>
        <view class="filter-section">
          <text class="filter-section__title">省</text>
          <wd-select-picker
            v-model="filters.provinceId!"
            use-default-slot
            type="radio"
            :columns="provinceOptions"
            custom-class="filter-section__picker"
            @confirm="handleProviceConfirm"
          >
            <text
              class="picker__text"
              :class="[!filters.provinceId && 'picker__text--placeholder']"
              >{{ provinceMap[filters.provinceId || ''] || '选择省' }}</text
            >
            <text class="i-carbon-chevron-down picker__icon"></text>
          </wd-select-picker>
        </view>
        <view class="filter-section">
          <text class="filter-section__title">市</text>
          <wd-select-picker
            v-model="filters.cityId!"
            use-default-slot
            type="radio"
            :columns="cityOptions"
            custom-class="filter-section__picker"
            @confirm="handleCityConfirm"
          >
            <text class="picker__text" :class="[!filters.cityId && 'picker__text--placeholder']">{{
              cityMap[filters.cityId || ''] || '选择市'
            }}</text>
            <text class="i-carbon-chevron-down picker__icon"></text>
          </wd-select-picker>
        </view>
        <view class="filter-section">
          <text class="filter-section__title">区</text>
          <wd-select-picker
            v-model="filters.regionId!"
            use-default-slot
            type="radio"
            :columns="regionOptions"
            custom-class="filter-section__picker"
          >
            <text
              class="picker__text"
              :class="[!filters.regionId && 'picker__text--placeholder']"
              >{{ regionMap[filters.regionId || ''] || '选择区' }}</text
            >
            <text class="i-carbon-chevron-down picker__icon"></text>
          </wd-select-picker>
        </view>
        <view class="filter-section">
          <text class="filter-section__title">巡检进度</text>
          <view class="progress-range">
            <wd-input-number
              v-model="filters.progressMin!"
              allow-null
              placeholder="最小进度"
              clearable
              :min="0"
              :max="100"
              custom-class="progress-range__number"
            />
            <text class="progress-range__separator">至</text>
            <wd-input-number
              v-model="filters.progressMax!"
              allow-null
              placeholder="最大进度"
              clearable
              :min="0"
              :max="100"
              custom-class="progress-range__number"
            />
          </view>
        </view>
      </view>

      <view class="filter-popup__footer">
        <view class="filter-popup__button-group">
          <wd-button
            type="info"
            :round="false"
            custom-class="reset-button"
            plain
            @click="handleReset"
          >
            重置
          </wd-button>
          <wd-button
            type="primary"
            :round="false"
            custom-class="submit-button"
            @click="handleApply"
          >
            提交
          </wd-button>
        </view>
      </view>
    </view>
  </wd-popup>
</template>

<style scoped lang="scss">
.filter-popup {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #f2f2f2;
  }

  &__title {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
  }

  &__close {
    display: flex;
    gap: 4px;
    align-items: center;
    font-size: 14px;
    color: #909399;
  }

  &__close-icon {
    font-size: 16px;
  }

  &__content {
    box-sizing: border-box;
    flex: 1;
    padding: 16px;
    overflow-y: auto;
  }

  &__footer {
    padding: 12px 16px;
    background-color: #fff;
    border-top: 1px solid #f2f2f2;
  }

  &__button-group {
    display: flex;
    gap: 12px;
  }
}

.filter-section {
  margin-bottom: 20px;

  &__title {
    display: block;
    margin-bottom: 12px;
    font-size: 16px;
    color: #303133;
  }

  &__input {
    box-sizing: border-box;
    height: 40px;
    padding: 0 12px;
    background: #f5f7fa;
    border-radius: 8px;

    --wot-input-inner-height: 40px;

    &::after {
      content: none;
    }
  }

  &__picker {
    box-sizing: border-box;
    height: 40px;
    padding: 8px 12px;
    background: #f5f7fa;
    border-radius: 8px;

    :deep(.wd-select-picker__field) {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }

    .picker__text {
      font-size: 15px;
      color: #303133;

      &--placeholder {
        color: var(--wot-input-placeholder-color, #c0c4cc);
      }
    }

    .picker__icon {
      color: #c0c4cc;
    }
  }
}

.progress-range {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 12px;

  --wot-input-number-height: 40px;
  --wot-input-number-btn-width: 40px;

  // --wot-input-number-input-width: calc(50% - 40px);

  &__number {
    display: flex;
    flex: 1;

    :deep(.wd-input-number__inner) {
      flex: 1;
      background-color: #f5f7fa;

      .wd-input-number__input {
        width: 100%;
      }
    }

    :deep(.uni-input-input) {
      width: calc(50% - 4px);
    }

    // &__number {
    //   flex: 1;
    //   display: flex;
    //   align-items: center;
    //   justify-content: space-between;
    //   // padding: 8px 10px;
    //   border: 1px solid #dcdfe6;
    //   border-radius: 4px;
    //   background-color: #f5f7fa;
    //   height: 40px;
    //   box-sizing: border-box;
    //   min-width: 0;

    //   // :deep(.wd-calendar__field) {
    //   //   width: 100%;
    //   //   display: flex;
    //   //   justify-content: space-between;
    //   //   align-items: center;
    //   // }
    // }
  }

  &__date-text {
    font-size: 14px;
    color: #606266;

    &--placeholder {
      color: var(--wot-input-placeholder-color, #c0c4cc);
    }
  }

  &__icon {
    font-size: 16px;
    color: #909399;
  }

  &__separator {
    font-size: 14px;
    color: #909399;
  }
}

.quick-time-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

:deep(.filter-radio) {
  margin-right: 10px !important; // 覆盖wot-design的内联样式
  margin-bottom: 8px; // 增加选项间距
}

:deep(.submit-button) {
  flex: 1;
  font-size: 16px;
}

:deep(.reset-button) {
  flex: 1;
  font-size: 16px;
}
</style>
