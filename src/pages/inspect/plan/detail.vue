<script setup lang="ts">
import type {
  InspectionPlanStatistics,
  InspectWorkOrderStatisticsParams,
} from '@/types/api/Inspect'
import { getInspectionWorkOrderStatistics } from '@/api'
import { pmSpecialFlag } from '@/constants/dict'
import { setNavigationBarTitle } from '@uni-helper/uni-promises'
import FilterPopup from './components/FilterPopup.vue'

const keyword = ref('')
const showFilterPopup = ref(false)
const currentFilters = ref({})

const paging = ref<ZPagingRef>()
const orderList = ref<InspectionPlanStatistics[]>([])
const pageSize = ref(20)
const planId = ref()

onLoad((options: any) => {
  planId.value = options.planId
  setNavigationBarTitle({ title: options.planName || '巡检计划明细' })
})

const onSearch = () => {
  paging.value?.reload()
}

const queryList = async (pageNum: number, pageSize: number) => {
  const params: InspectWorkOrderStatisticsParams = {
    pageNum,
    pageSize,
  }

  // 合并筛选条件
  Object.assign(params, currentFilters.value)

  if (keyword.value) {
    params.opName = keyword.value
  }

  try {
    const res = await getInspectionWorkOrderStatistics({ ...params, planId: planId.value })
    paging.value?.completeByTotal(res.content, res.totalElements)
  } catch {
    paging.value?.complete(false)
  }
}

const goToDetail = (plan: InspectionPlanStatistics) => {
  uni.navigateTo({
    url: `/pages/inspect/task/list?planId=${plan.planId}&opName=${plan.opName}&specialFlag=${plan.specialFlag}`,
  })
}

const handleApplyFilter = (filters: any) => {
  currentFilters.value = filters
  showFilterPopup.value = false
  paging.value?.reload()
}
</script>

<template>
  <view class="inspect-plan-detail-page">
    <!-- 搜索区域 -->
    <SearchBar
      v-model="keyword"
      :show-filter="true"
      placeholder="请输入运维商名称"
      @search="onSearch"
      @filter="showFilterPopup = true"
    />

    <!-- 工单列表 -->
    <z-paging
      ref="paging"
      v-model="orderList"
      class="plan-list-paging"
      :fixed="false"
      :default-page-size="pageSize"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
      @query="queryList"
    >
      <view class="plan-list-content">
        <view
          v-for="plan in orderList"
          :key="plan.planId"
          class="plan-card"
          @click="goToDetail(plan)"
        >
          <view class="card-header">
            <text class="title">{{ plan.opName }}</text>
            <wd-tag type="primary">{{ pmSpecialFlag[plan.specialFlag!] || '-' }}</wd-tag>
          </view>
          <view class="card-body">
            <view class="detail-item">
              <text class="label">巡检计划</text>
              <text class="value">{{ plan.planName }}</text>
            </view>
            <view class="detail-item">
              <text class="label">开始时间</text>
              <text class="value">{{ plan.startDate }}</text>
            </view>
            <view class="detail-item">
              <text class="label">结束时间</text>
              <text class="value">{{ plan.endDate }}</text>
            </view>
            <view class="detail-item">
              <text class="label">电站数量</text>
              <text class="value">{{ plan.stationCount }}</text>
            </view>
            <view class="detail-item">
              <text class="label">已巡检</text>
              <text class="value">{{ plan.inspectedCount }}</text>
            </view>
            <view class="detail-item">
              <text class="label">巡检进度</text>
              <text class="value flex-1">
                <wd-progress :percentage="plan.progress" custom-class="w-full px-1" />
              </text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
    <FilterPopup
      v-model:model-value="showFilterPopup"
      :show-op-condition="false"
      @apply="handleApplyFilter"
    />
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "巡检计划明细"
  }
}
</route>

<style scoped lang="scss">
.inspect-plan-detail-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .plan-list-paging {
    flex: 1;
  }

  .plan-list-content {
    padding: 8px 12px;
  }

  .plan-card {
    padding: 12px;
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 5%);

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 8px;
      margin-bottom: 10px;
      border-bottom: 1px solid #f0f0f0;

      .title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }
    }

    .card-body {
      .detail-item {
        display: flex;
        margin-bottom: 4px;
        font-size: 14px;
        line-height: 1.6;

        .label {
          flex-shrink: 0;
          width: 70px;
          margin-right: 8px;
          color: #666;
        }

        .value {
          color: #333;
          word-break: break-all;
        }
      }
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
