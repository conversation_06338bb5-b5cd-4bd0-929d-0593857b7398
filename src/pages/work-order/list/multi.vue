<script setup lang="ts">
import type { WorkOrder, WorkOrderPageParams } from '@/types/api/Workorder'
import type { SegmentedOption } from 'wot-design-uni/components/wd-segmented/types'
import {
  getWorkOrderHandledPage,
  getWorkOrderPage,
  getWorkOrderToAudit,
  getWorkOrderToDispatch,
} from '@/api/workorder'
import SearchBar from '@/components/SearchBar.vue'
import { APPROVE_STSTUS_GROUP, HANDLE_STSTUS_GROUP } from '@/constants/workorder'
import { useUserStore } from '@/store'
import { onLoad } from '@dcloudio/uni-app'
import { nextTick, ref, watch } from 'vue'
import FilterPopup from './components/FilterPopup.vue'

const keyword = ref('')
const activeTab = ref<string | number>('')
const showFilterPopup = ref(false)
const currentFilters = ref({})
const userStore = useUserStore()
const userInfo = userStore.userInfo

const tabs = ref<SegmentedOption[]>([
  {
    value: 'TO_DISPATCH',
    payload: {
      label: '未下发',
      badge: 0,
    },
  },
  {
    value: 'TO_AUDIT',
    payload: {
      label: '待审核',
      badge: 0,
    },
  },
  {
    value: 'CLOSED',
    payload: {
      label: '已关单',
      badge: 0,
    },
  },
  {
    value: 'FINISHED',
    payload: {
      label: '已完成',
      badge: 0,
    },
  },
  {
    value: 'ALL',
    payload: {
      label: '全部',
      badge: 0,
    },
  },
])

const paging = ref<any>()
const orderList = ref<WorkOrder[]>([])
const pageSize = ref(20)

const apiMap: Record<string, (params: any) => Promise<any>> = {
  TO_DISPATCH: getWorkOrderToDispatch,
  TO_PROCESS: getWorkOrderPage,
  HANDLED: getWorkOrderHandledPage,
  FINISHED: getWorkOrderPage,
  CLOSED: getWorkOrderPage,
  TO_AUDIT: getWorkOrderToAudit,
  ALL: getWorkOrderPage,
}

let type: string
onLoad((options: any) => {
  type = options.type
  if (type === 'approve') {
    tabs.value = APPROVE_STSTUS_GROUP
    activeTab.value = APPROVE_STSTUS_GROUP[0]?.value || 'TO_AUDIT'
  }
  else if (type === 'handle') {
    tabs.value = HANDLE_STSTUS_GROUP
    activeTab.value = HANDLE_STSTUS_GROUP[0]?.value || 'TO_PROCESS'
  }
  else {
    activeTab.value = tabs.value[0]?.value || 'TO_DISPATCH'
  }
})

const handleWorkOrderUpdated = (data: {
  orderCode?: string
  updatedWorkOrder?: WorkOrder
  needsRefresh?: boolean
}) => {
  if (data.needsRefresh) {
    if (paging.value) {
      paging.value.reload()
    }
    return
  }

  if (!data.orderCode || !data.updatedWorkOrder) {
    return
  }

  const { orderCode, updatedWorkOrder } = data
  const index = orderList.value.findIndex((item: WorkOrder) => item.orderCode === orderCode)

  if (index !== -1) {
    let shouldRemainInCurrentTab = true
    if (activeTab.value !== 'ALL') {
      shouldRemainInCurrentTab = false
    }

    if (shouldRemainInCurrentTab) {
      orderList.value.splice(index, 1, updatedWorkOrder)
    }
    else {
      if (orderList.value.length === 1 && paging.value) {
        paging.value.reload()
      }
      else {
        orderList.value.splice(index, 1)
      }
    }
  }
  else if (paging.value) {
    paging.value.reload()
  }
}

const onSearch = () => {
  paging.value?.reload()
}

const queryList = async (pageNum: number, pageSize: number) => {
  const params: WorkOrderPageParams = {
    pageNum,
    pageSize,
  }

  if (keyword.value) {
    if (/^\d*$/.test(keyword.value)) {
      params.stationCode = keyword.value
    }
    else {
      params.stationName = keyword.value
    }
  }

  // 合并筛选条件
  Object.assign(params, currentFilters.value)

  if (activeTab.value === 'HANDLED') {
    params.orderStatus = undefined
  }
  else if (activeTab.value !== 'ALL') {
    params.orderStatus = activeTab.value as string
  }

  try {
    const api = apiMap[activeTab.value as string]
    if (!api) {
      paging.value?.complete(false)
      return
    }
    const res = await api(params)
    paging.value?.completeByTotal(res.content, res.totalElements)
  }
  catch {
    paging.value?.complete(false)
  }
}

const goToDetail = (order: WorkOrder) => {
  let action = 'approve'
  if (activeTab.value === 'HANDLED') {
    action = 'view'
  }
  else if (['FINISHED'].includes(order.orderStatus!)) {
    if (order.overTime === true) {
      action = 'appeal'
    }
    else {
      action = 'view'
    }
  }
  else if (['CLOSED'].includes(order.orderStatus!)) {
    action = 'view'
  }
  else if (order.orderStatus === 'TO_PROCESS') {
    action = 'handle'
  }
  else if (order.orderStatus === 'TO_ASSIGN') {
    if (userInfo?.userType === 'merchant' && userInfo?.isProvider === true) {
      action = 'assign'
    }
    else {
      action = 'view'
    }
  }
  else if (order.orderStatus === 'TO_SUB_CENTER_DISPATCH') {
    if (userInfo?.userType === 'haier' && userInfo?.subCenterUser === false) {
      action = 'view'
    }
  }
  else if (order.orderStatus === 'TO_HEAD_DISPATCH') {
    if (userInfo?.userType === 'haier' && userInfo?.subCenterUser === true) {
      action = 'view'
    }
  }
  else if (order.orderStatus === 'TO_SUB_CENTER_AUDIT') {
    if (userInfo?.userType === 'haier' && userInfo?.subCenterUser === false) {
      action = 'view'
    }
  }
  else if (order.orderStatus === 'TO_HEAD_AUDIT') {
    if (userInfo?.userType === 'haier' && userInfo?.subCenterUser === true) {
      action = 'view'
    }
  }
  uni.navigateTo({
    url: `/pages/work-order/index?orderCode=${order.orderCode}&action=${action}`,
    events: {
      workOrderUpdated: handleWorkOrderUpdated,
    },
  })
}

const handleApplyFilter = (filters: any) => {
  currentFilters.value = filters
  showFilterPopup.value = false
  paging.value?.reload()
}

const getOrderStatusText = (status?: string) => {
  if (!status) {
    return ''
  }
  const statusMap: Record<string, string> = {
    TO_HEAD_DISPATCH: '待总部下发',
    TO_SUB_CENTER_DISPATCH: '待分中心下发',
    TO_ASSIGN: '待指派',
    TO_PROCESS: '待处理',
    HANDLED: '已处理',
    TO_SUB_CENTER_AUDIT: '待分中心审核',
    TO_HEAD_AUDIT: '待总部审核',
    FINISHED: '已完成',
    CLOSED: '已关单',
  }
  return statusMap[status] || status
}

watch(activeTab, async () => {
  if (paging.value) {
    orderList.value = []
    await nextTick()
  }
  paging.value?.reload()
})
</script>

<template>
  <view class="work-order-list-page">
    <!-- 搜索区域 -->
    <SearchBar
      v-model="keyword"
      :show-filter="true"
      placeholder="请输入电站编号/电站名称"
      @search="onSearch"
      @filter="showFilterPopup = true"
    />

    <!-- Tab 切换 -->
    <view class="segmented-container">
      <wd-segmented v-model:value="activeTab" :options="tabs">
        <template #label="{ option }">
          <view class="segmented-item-content">
            <text>{{ option.payload.label }}</text>
            <wd-badge
              v-if="option.payload.badge"
              :model-value="option.payload.badge"
              custom-class="segmented-item-badge"
              type="danger"
            />
          </view>
        </template>
      </wd-segmented>
    </view>

    <!-- 工单列表 -->
    <z-paging
      ref="paging"
      v-model="orderList"
      class="order-list-paging"
      :fixed="false"
      :default-page-size="pageSize"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
      @query="queryList"
    >
      <view class="order-list-content">
        <view
          v-for="order in orderList"
          :key="order.id"
          class="order-card"
          @click="goToDetail(order)"
        >
          <view class="card-header">
            <text class="title">{{ order.orderName }}</text>
            <wd-tag type="primary">{{ getOrderStatusText(order.orderStatus) }}</wd-tag>
          </view>
          <view class="card-meta">
            <text>{{ order.createdAt }}</text>
          </view>
          <view class="card-body">
            <view class="detail-item">
              <text class="label">电站编码</text>
              <text class="value">{{ order.stationCode }}</text>
            </view>
            <view class="detail-item">
              <text class="label">电站名称</text>
              <text class="value">{{ order.stationName }}</text>
            </view>
            <view class="detail-item">
              <text class="label">联系方式</text>
              <text class="value">{{ order.stationPhone }}</text>
            </view>
            <view class="detail-item">
              <text class="label">电站地址</text>
              <text class="value">{{ order.address }}</text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>

    <FilterPopup
      v-model:model-value="showFilterPopup"
      :show-status-condition="activeTab === 'ALL'"
      @apply="handleApplyFilter"
    />
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "工单列表"
  }
}
</route>

<style scoped lang="scss">
.work-order-list-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .segmented-container {
    padding: 8px 12px;

    --wot-segmented-item-bg-color: #fff;

    :deep(.wd-segmented__item.is-active) {
      color: white;
      background-color: $uni-color-primary;
    }

    :deep(.wd-segmented__item) {
      min-width: none;
      color: #91929e;
    }

    :deep(.wd-segmented) {
      width: 100%;
    }
  }

  .segmented-item-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    .segmented-item-badge {
      position: absolute;
      top: 2px;
      right: -5px;
      transform: translate(50%, -50%) scale(0.8);
    }
  }

  .order-list-paging {
    flex: 1;
  }

  .order-list-content {
    padding: 8px 12px;
  }

  .order-card {
    padding: 12px;
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 5%);

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        flex: 1;
        margin-right: 4px;
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }
    }

    .card-meta {
      padding-bottom: 8px;
      margin-bottom: 10px;
      font-size: 12px;
      color: #999;
      border-bottom: 1px solid #f0f0f0;
    }

    .card-body {
      .detail-item {
        display: flex;
        margin-bottom: 4px;
        font-size: 14px;
        line-height: 1.6;

        .label {
          flex-shrink: 0;
          width: 70px;
          margin-right: 8px;
          color: #666;
        }

        .value {
          color: #333;
          word-break: break-all;
        }
      }
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
