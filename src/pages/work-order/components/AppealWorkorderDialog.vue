<script setup lang="ts">
import type { FormItemConfig } from '@/components/DynamicForm.vue'
import type { WorkOrderAppealReq } from '@/types/api/Workorder'
import { submitAppealWorkOrder } from '@/api/workorder'
import DialogBox from '@/components/DialogBox.vue'
import DynamicForm from '@/components/DynamicForm.vue'
import { useUserStore } from '@/store'
import { ref } from 'vue'
import { useToast } from 'wot-design-uni'

const props = defineProps<{
  orderCode: string
}>()

const emit = defineEmits(['success'])

const userStore = useUserStore()
const toast = useToast()
const formRef = ref()
const dialogRef = ref()

const appealFormData = ref<WorkOrderAppealReq>({
  orderCode: '',
  appealDescription: '',
  appealUrls: '',
  submitter: userStore.userInfo?.name,
})

const formConfig: FormItemConfig[] = [
  {
    type: 'textarea' as const,
    field: 'appealDescription',
    label: '申诉描述',
    required: true,
    rules: [{ required: true, message: '请输入申诉描述' }],
    attrs: {
      maxlength: 200,
      placeholder: '请输入申诉描述',
    },
  },
  {
    type: 'upload',
    field: 'appealUrls',
    label: '上传附件',
    required: true,
    attrs: {
      maxCount: 3,
      hint: '· 限制上传 3 个图片',
      sourceType: ['camera', 'album'],
    },
  },
]

const showAppealForm = () => {
  appealFormData.value = {
    orderCode: props.orderCode,
    appealDescription: '',
    appealUrls: '',
  }
  dialogRef.value.open()
}

const handleConfirm = async () => {
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      toast.loading('正在提交...')
      await submitAppealWorkOrder({
        ...appealFormData.value,
        appealUrls: appealFormData.value.appealUrls?.map((item: any) => item.url).join(','),
      })
      toast.success('申诉提交成功')
      emit('success')
      dialogRef.value.close()
    }
  }
  finally {
    toast.close()
  }
}

defineExpose({
  showAppealForm,
})
</script>

<template>
  <DialogBox
    ref="dialogRef"
    selector="appeal-workorder-dialog"
    title="超期申诉"
    @confirm="handleConfirm"
  >
    <view class="dialog-content">
      <DynamicForm ref="formRef" v-model="appealFormData" :config="formConfig" label-position="top" />
    </view>
  </DialogBox>
</template>

<style lang="scss" scoped>
.dialog-content {
  max-height: 48vh;
  overflow-y: auto;
}
</style>
