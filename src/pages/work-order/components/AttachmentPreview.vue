<script setup lang="ts">
import { ref, computed } from 'vue'

interface AttachmentFile {
  detailId: string
  fileUrl: string
  fileName?: string
}

const props = defineProps<{
  fileList: AttachmentFile[]
}>()

const visible = defineModel<boolean>('visible', { default: false })

// 附件类型映射
const attachmentTypeMap: Record<string, string> = {
  damagedPart: '损坏部件',
  parallelMeshBox: '并网箱',
  Nameplate: '铭牌'
}

// 处理后的附件列表
const processedFileList = computed(() => {
  return props.fileList.map(file => ({
    ...file,
    typeName: attachmentTypeMap[file.detailId] || file.detailId,
    fileName: file.fileName || `${attachmentTypeMap[file.detailId] || file.detailId}.jpg`
  }))
})

// 预览图片
const previewImage = (url: string, index: number) => {
  const urls = processedFileList.value.map(file => file.fileUrl)
  uni.previewImage({
    current: index,
    urls: urls
  })
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}
</script>

<template>
  <wd-popup
    v-model="visible"
    position="bottom"
    :safe-area-inset-bottom="true"
    custom-style="border-radius: 20px 20px 0 0;"
  >
    <view class="attachment-preview">
      <view class="preview-header">
        <view class="header-title">附件预览</view>
        <view class="close-btn" @click="handleClose">
          <wd-icon name="close" size="20px" color="#666" />
        </view>
      </view>

      <view class="preview-content">
        <view v-if="processedFileList.length === 0" class="empty-state">
          <view class="empty-icon">📎</view>
          <view class="empty-text">暂无附件</view>
        </view>

        <view v-else class="attachment-list">
          <view
            v-for="(file, index) in processedFileList"
            :key="file.detailId"
            class="attachment-item"
            @click="previewImage(file.fileUrl, index)"
          >
            <view class="attachment-info">
              <view class="attachment-type">{{ file.typeName }}</view>
              <view class="attachment-name">{{ file.fileName }}</view>
            </view>
            <view class="attachment-preview-img">
              <image
                :src="file.fileUrl"
                mode="aspectFill"
                class="preview-img"
                @error="() => {}"
              />
            </view>
            <view class="attachment-action">
              <wd-icon name="arrow-right" size="16px" color="#999" />
            </view>
          </view>
        </view>
      </view>

      <view class="preview-footer">
        <wd-button
          :round="false"
          type="info"
          plain
          custom-class="footer-btn"
          @click="handleClose"
        >
          关闭
        </wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<style scoped lang="scss">
.attachment-preview {
  background-color: #fff;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 16px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.close-btn {
  padding: 4px;
  cursor: pointer;
}

.preview-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;

  &:active {
    background-color: #e9ecef;
  }
}

.attachment-info {
  flex: 1;
  margin-right: 12px;
}

.attachment-type {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.attachment-name {
  font-size: 12px;
  color: #666;
}

.attachment-preview-img {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 12px;
}

.preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.attachment-action {
  display: flex;
  align-items: center;
}

.preview-footer {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.footer-btn {
  width: 100%;
}
</style>
