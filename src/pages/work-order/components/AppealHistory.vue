<script setup lang="ts">
import type { WorkOrderAppealInfo } from '@/types/api/Workorder'

defineProps<{
  appeals: WorkOrderAppealInfo[]
}>()
</script>

<template>
  <view class="appeal-history">
    <wd-steps :active="appeals.length - 1" vertical>
      <wd-step v-for="(appeal, index) in appeals" :key="index">
        <template #title>
          <view class="flex justify-between">
            <text>{{ appeal.submitter }} 提交申诉</text>
            <text class="text-xs text-gray-400">{{ appeal.submitTime }}</text>
          </view>
        </template>
        <template #description>
          <view class="description-content">
            <view>申诉描述：{{ appeal.appealDescription }}</view>
            <view v-if="appeal.appealUrls">
              <text>附件：</text>
              <image
                v-for="(url, i) in appeal.appealUrls.split(',')"
                :key="i"
                :src="url"
                class="appeal-image"
                mode="aspectFill"
              />
            </view>
            <view v-if="appeal.appealStatus === 'AUDIT_OK'">
              <view class="text-green-500">审核通过 ({{ appeal.auditTime }})</view>
              <view>审核人：{{ appeal.auditBy }}</view>
              <view v-if="appeal.auditRemark">审核备注：{{ appeal.auditRemark }}</view>
            </view>
            <view v-else-if="appeal.appealStatus === 'AUDIT_REJECT'">
              <view class="text-red-500">审核驳回 ({{ appeal.auditTime }})</view>
              <view>审核人：{{ appeal.auditBy }}</view>
              <view v-if="appeal.auditRemark">审核备注：{{ appeal.auditRemark }}</view>
            </view>
            <view v-else-if="appeal.appealStatus === 'WAIT_AUDIT'">
              <view class="text-yellow-500">待审核</view>
            </view>
          </view>
        </template>
      </wd-step>
    </wd-steps>
  </view>
</template>

<style lang="scss" scoped>
.appeal-history {
  .appeal-image {
    width: 60px;
    height: 60px;
    margin-right: 8px;
    border-radius: 4px;
  }
  .description-content {
    font-size: 12px;
    color: #666;
    line-height: 1.8;
  }
}
</style>
