<script setup lang="ts">
import type { FormItemConfig } from '@/components/DynamicForm.vue'
import type { WorkOrderAppealAuditReq, WorkOrderAppealInfo } from '@/types/api/Workorder'
import { auditAppealWorkOrder } from '@/api/workorder'
import DialogBox from '@/components/DialogBox.vue'
import DynamicForm from '@/components/DynamicForm.vue'
import { computed, ref } from 'vue'
import { useToast } from 'wot-design-uni'

const emit = defineEmits(['success'])
const toast = useToast()
const dialogRef = ref()
const formRef = ref()
const appealInfo = ref<WorkOrderAppealInfo>()

const formData = ref<Partial<WorkOrderAppealAuditReq>>({
  appealStatus: 'AUDIT_OK',
  auditRemark: '',
})

const formConfig = computed<FormItemConfig[]>(() => [
  {
    type: 'radio',
    field: 'appealStatus',
    label: '审核结果',
    required: true,
    options: [
      { label: '通过', value: 'AUDIT_OK' },
      { label: '驳回', value: 'AUDIT_REJECT' },
    ],
    attrs: {
      inline: true,
      shape: 'dot',
    },
  },
  {
    type: 'textarea',
    field: 'auditRemark',
    label: '审核备注',
    required: formData.value.appealStatus === 'AUDIT_REJECT',
    attrs: {
      placeholder: '审核驳回时必填',
      maxlength: 200,
    },
  },
])

const show = (data: WorkOrderAppealInfo) => {
  appealInfo.value = data
  formData.value = {
    appealStatus: 'AUDIT_OK',
    auditRemark: '',
  }
  dialogRef.value.open()
}

const submitAudit = async () => {
  if (!appealInfo.value?.orderCode)
    return

  const valid = await formRef.value.validate()
  if (!valid)
    return

  try {
    toast.loading('正在提交...')
    await auditAppealWorkOrder({
      orderCode: appealInfo.value.orderCode,
      appealStatus: formData.value.appealStatus!,
      auditRemark: formData.value.auditRemark,
    })
    toast.success('审核提交成功')
    emit('success')
    dialogRef.value.close()
  }
  finally {
    toast.close()
  }
}

defineExpose({ show })
</script>

<template>
  <DialogBox ref="dialogRef" title="申诉审核" @confirm="submitAudit">
    <view class="audit-dialog-content">
      <view class="info-section">
        <view class="info-item">
          <text class="label">申诉人</text>
          <text class="value">{{ appealInfo?.submitter }}</text>
        </view>
        <view class="info-item">
          <text class="label">申诉时间</text>
          <text class="value">{{ appealInfo?.submitTime }}</text>
        </view>
        <view class="info-item">
          <text class="label">申诉描述</text>
          <text class="value">{{ appealInfo?.appealDescription }}</text>
        </view>
        <view v-if="appealInfo?.appealUrls" class="info-item">
          <text class="label">附件</text>
          <view class="value image-gallery">
            <image
              v-for="(url, i) in appealInfo.appealUrls.split(',')"
              :key="i"
              :src="url"
              class="appeal-image"
              mode="aspectFill"
            />
          </view>
        </view>
      </view>

      <DynamicForm
        ref="formRef"
        v-model="formData"
        :config="formConfig"
        label-position="top"
      />
    </view>
  </DialogBox>
</template>

<style lang="scss" scoped>
.audit-dialog-content {
  max-height: 48vh;
  overflow-y: auto;
}

.info-section {
  margin-bottom: 16px;

  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    font-size: 14px;
    line-height: 1.6;

    .label {
      flex: 0 0 80px;
      width: 70px;
      color: #666;
      text-align: left;
    }

    .value {
      text-align: left;
      flex: 1;
      color: #333;
    }
  }

  .image-gallery {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .appeal-image {
    width: 70px;
    height: 70px;
    border-radius: 4px;
  }
}
</style>
