<script setup>
import { useDictStore } from '@/store/modules/dict'
import { computed } from 'vue'

const props = defineProps({
  faultInfo: {
    type: Object,
    default: () => ({}),
  },
  photos: {
    type: Array,
    default: () => [],
  },
})

const dictStore = useDictStore()

const faultLevelLabel = computed(() => {
  return dictStore.getDictLabel('fault_level', props.faultInfo?.faultLevel)
})

const previewImages = index => {
  uni.previewImage({
    urls: props.photos,
    current: props.photos[index],
  })
}
</script>

<template>
  <view class="card m-3 rounded-lg bg-white p-4 shadow-sm">
    <view class="card-title mb-3 flex items-center">
      <view class="accent-bar mr-2 h-4 w-1 bg-primary"></view>
      <text class="text-sm text-[#4B4B4B] font-bold">故障信息</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">故障名称</text>
      <text class="value">{{ faultInfo?.faultName || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">故障等级</text>
      <text class="value">{{ faultLevelLabel }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">故障描述</text>
      <text class="value">{{ faultInfo?.faultDetails || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">故障照片</text>
      <view class="value flex flex-wrap gap-4">
        <wd-img
          v-for="(img, index) in photos"
          :key="index"
          :src="img"
          custom-class="w-25 h-25 rounded-md bg-gray-200"
          @click="previewImages(index)"
        />
      </view>
    </view>
  </view>
</template>

<style scoped>
.detail-row {
  display: flex;
  margin-bottom: 10px;
}

.label {
  width: 88px;
  font-size: 14px;
  color: #999;
}

.value {
  flex: 1;
  font-size: 14px;
  color: #333;
}
</style>
