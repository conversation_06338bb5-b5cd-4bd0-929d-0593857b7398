<script setup lang="ts">
import type { WorkOrder } from '@/types/api/Workorder'
import { useDictStore } from '@/store/modules/dict'

defineProps<{
  workorder: WorkOrder
  title?: string
}>()

const dictStore = useDictStore()
</script>

<template>
  <view class="card m-3 rounded-lg bg-white p-4 shadow-sm">
    <view class="card-title mb-3 flex items-center">
      <view class="accent-bar mr-2 h-4 w-1 bg-primary"></view>
      <text class="text-sm text-[#4B4B4B] font-bold">{{ title || '工单信息' }}</text>
    </view>
    <view class="detail-row">
      <text class="label">运维单号</text>
      <text class="value">{{ workorder.orderCode || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">工单状态</text>
      <text class="value">{{
        dictStore.getDictLabel('work_order_status', workorder.orderStatus)
      }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">工单类型</text>
      <text class="value">{{
        dictStore.getDictLabel('work_order_type', workorder.orderType)
      }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">工单来源</text>
      <text class="value">{{
        dictStore.getDictLabel('work_order_source', workorder.orderSource)
      }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">截止时间</text>
      <text class="value">{{ workorder.deadline || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">下发方式</text>
      <text class="value">{{
        dictStore.getDictLabel('dispatch_mode', workorder.dispatchMode)
      }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">审核权限</text>
      <text class="value">{{
        dictStore.getDictLabel('dispatch_review_permission', workorder.dispatchAuditPermission)
      }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">审核方式</text>
      <text class="value">{{ dictStore.getDictLabel('review_mode', workorder.auditMode) }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">是否超时</text>
      <text class="value">{{
        typeof workorder.overTime === 'boolean' ? (workorder.overTime ? '是' : '否') : '-'
      }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">创建人</text>
      <text class="value">{{ workorder.createdBy || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">创建时间</text>
      <text class="value">{{ workorder.createdAt || '-' }}</text>
    </view>
  </view>
</template>

<style scoped>
.detail-row {
  display: flex;
  align-items: flex-start;
  font-size: 14px;
  line-height: 1.5;
}

.label {
  width: 88px;
  color: #91929e;
}

.value {
  flex-grow: 1;
  color: #4b4b4b;
  word-break: break-all;
}
</style>
