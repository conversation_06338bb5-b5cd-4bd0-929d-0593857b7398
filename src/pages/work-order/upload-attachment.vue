<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { saveWorkorderSparePartsFile } from '@/api/workorder'
import { useToast } from 'wot-design-uni'
import { onLoad } from '@dcloudio/uni-app'

const toast = useToast()

interface FormData {
  damagedPart: string
  parallelMeshBox: string
  Nameplate: string
}

const orderCode = ref('')
const formData = ref<FormData>({
  damagedPart: '',
  parallelMeshBox: '',
  Nameplate: ''
})

const isSubmitting = ref(false)

// 动态表单配置
const formConfig = ref([
  {
    type: 'upload',
    field: 'damagedPart',
    title: '损坏部件',
    props: {
      multiple: false,
      maxCount: 1,
      accept: 'image/*',
      action: '', // 这里可以配置上传地址
    }
  },
  {
    type: 'upload',
    field: 'parallelMeshBox',
    title: '并网箱',
    props: {
      multiple: false,
      maxCount: 1,
      accept: 'image/*',
      action: '',
    }
  },
  {
    type: 'upload',
    field: 'Nameplate',
    title: '铭牌',
    props: {
      multiple: false,
      maxCount: 1,
      accept: 'image/*',
      action: '',
    }
  }
])

onLoad((options?: Record<string, string>) => {
  if (options?.orderCode) {
    orderCode.value = options.orderCode
  }
})

// 提交附件
const handleSubmit = async () => {
  if (isSubmitting.value) return
  
  // 检查是否至少上传了一个附件
  const hasAnyFile = formData.value.damagedPart || 
                     formData.value.parallelMeshBox || 
                     formData.value.Nameplate
  
  if (!hasAnyFile) {
    toast.warning('请至少上传一个附件')
    return
  }

  isSubmitting.value = true
  
  try {
    const fileList = [
      {
        detailId: 'damagedPart',
        fileUrl: formData.value.damagedPart
      },
      {
        detailId: 'parallelMeshBox',
        fileUrl: formData.value.parallelMeshBox
      },
      {
        detailId: 'Nameplate',
        fileUrl: formData.value.Nameplate
      }
    ].filter(item => item.fileUrl) // 过滤掉空的文件

    const params = {
      serviceInfoId: orderCode.value,
      fileList
    }

    await saveWorkorderSparePartsFile(params)
    toast.success('附件上传成功')
    
    // 返回上一页并传递成功标识
    uni.navigateBack({
      success: () => {
        // 可以通过事件通知上一页刷新
        uni.$emit('attachmentUploaded', { success: true })
      }
    })
  } catch (error) {
    console.error('上传附件失败:', error)
    toast.error('上传附件失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}

// 取消操作
const handleCancel = () => {
  uni.navigateBack()
}
</script>

<template>
  <view class="upload-attachment-page">
    <view class="page-header">
      <view class="header-content">
        <view class="title">上传附件</view>
        <view class="subtitle">工单编号：{{ orderCode }}</view>
      </view>
    </view>

    <view class="form-container">
      <view class="form-section">
        <view class="section-title">请上传相关附件</view>
        
        <!-- 使用简单的上传组件替代dynamicform -->
        <view class="upload-item" v-for="config in formConfig" :key="config.field">
          <view class="upload-label">{{ config.title }}</view>
          <wd-upload
            v-model="formData[config.field as keyof FormData]"
            :max-count="1"
            :accept="config.props.accept"
            :multiple="false"
            action=""
            custom-class="upload-component"
          />
        </view>
      </view>
    </view>

    <view class="bottom-actions">
      <wd-button 
        :round="false" 
        type="info" 
        plain 
        custom-class="cancel-btn"
        @click="handleCancel"
      >
        取消
      </wd-button>
      <wd-button 
        :round="false" 
        type="primary" 
        custom-class="submit-btn"
        :loading="isSubmitting"
        @click="handleSubmit"
      >
        提交
      </wd-button>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "上传附件"
  }
}
</route>

<style scoped lang="scss">
.upload-attachment-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 80px;
}

.page-header {
  background-color: #fff;
  padding: 20px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-content {
  .title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
  }
  
  .subtitle {
    font-size: 14px;
    color: #666;
  }
}

.form-container {
  padding: 16px;
}

.form-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.upload-item {
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.upload-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.upload-component {
  width: 100%;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 12px;
  padding: 12px 16px;
  background-color: #fff;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.cancel-btn {
  flex: 1;
}

.submit-btn {
  flex: 2;
}
</style>
