<script setup lang="ts">
import { ref } from 'vue'
import { applyWorkorderSpareParts } from '@/api/workorder'
import { useToast } from 'wot-design-uni'
import { onLoad } from '@dcloudio/uni-app'
import Navbar from '@/components/Navbar.vue'
import DynamicForm from '@/components/DynamicForm.vue'
import type { FormItemConfig } from '@/components/DynamicForm.vue'

const toast = useToast()

interface FormData {
  partNo: string
  partName: string
  applyNum: number
  partFault: string
  oldpartReturn: string
  oldReturnResult: string
  remarks: string
}

const orderCode = ref('')
const formData = ref<FormData>({
  partNo: '',
  partName: '',
  applyNum: 1,
  partFault: '',
  oldpartReturn: '1', // 默认有旧件返还
  oldReturnResult: '',
  remarks: ''
})

const isSubmitting = ref(false)
const formRef = ref()

// 动态表单配置
const formConfig: FormItemConfig[] = [
  {
    type: 'input',
    field: 'partNo',
    label: '备件编号',
    required: true,
    attrs: {
      placeholder: '请输入备件编号',
      clearable: true
    }
  },
  {
    type: 'input',
    field: 'partName',
    label: '备件名称',
    required: true,
    attrs: {
      placeholder: '请输入备件名称',
      clearable: true
    }
  },
  {
    type: 'number',
    field: 'applyNum',
    label: '申请数量',
    required: true,
    attrs: {
      placeholder: '请输入申请数量',
      min: 1,
      max: 999
    }
  },
  {
    type: 'textarea',
    field: 'partFault',
    label: '性能故障描述',
    required: true,
    attrs: {
      placeholder: '请详细描述备件故障情况',
      maxlength: 500,
      showWordLimit: true,
      autoHeight: true
    }
  },
  {
    type: 'radio',
    field: 'oldpartReturn',
    label: '有无旧件返还',
    required: true,
    attrs: {
      options: [
        { label: '有旧件返还', value: '1' },
        { label: '无旧件返还', value: '0' }
      ]
    }
  },
  {
    type: 'textarea',
    field: 'oldReturnResult',
    label: '无旧件返还原因',
    required: false,
    show: (data: FormData) => data.oldpartReturn === '0',
    attrs: {
      placeholder: '请说明无旧件返还的原因',
      maxlength: 200,
      showWordLimit: true,
      autoHeight: true
    }
  },
  {
    type: 'textarea',
    field: 'remarks',
    label: '备注',
    required: false,
    attrs: {
      placeholder: '请输入备注信息（选填）',
      maxlength: 200,
      showWordLimit: true,
      autoHeight: true
    }
  }
]

onLoad((options?: Record<string, string>) => {
  if (options?.orderCode) {
    orderCode.value = options.orderCode
  }
})

// 提交申请
const handleSubmit = async () => {
  if (isSubmitting.value) return
  
  // 表单验证
  const isValid = await formRef.value?.validate()
  if (!isValid) {
    toast.warning('请完善必填信息')
    return
  }

  isSubmitting.value = true
  
  try {
    const params = {
      serviceInfoId: orderCode.value,
      partNo: formData.value.partNo,
      partName: formData.value.partName,
      applyNum: formData.value.applyNum,
      partFault: formData.value.partFault,
      oldpartReturn: formData.value.oldpartReturn,
      oldReturnResult: formData.value.oldpartReturn === '0' ? formData.value.oldReturnResult : '',
      remarks: formData.value.remarks,
      applySource: 'APP', // 申请渠道
      poolId: '申请中', // 申请状态
      activeFlag: '1' // 数据状态
    }

    await applyWorkorderSpareParts(params)
    toast.success('备件申请提交成功')
    
    // 返回上一页并传递成功标识
    uni.navigateBack({
      success: () => {
        // 可以通过事件通知上一页刷新
        uni.$emit('sparePartApplied', { success: true })
      }
    })
  } catch (error) {
    console.error('申请备件失败:', error)
    toast.error('申请备件失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}

// 取消操作
const handleCancel = () => {
  uni.navigateBack()
}
</script>

<template>
  <view class="apply-spare-part-page">
    <Navbar title="申请备件" :show-back="true" :safe-area-inset-top="true" :placeholder="true" />
    
    <view class="page-header">
      <view class="header-content">
        <view class="subtitle">工单编号：{{ orderCode }}</view>
      </view>
    </view>

    <view class="form-container">
      <view class="form-section">
        <view class="section-title">备件申请信息</view>
        
        <!-- 使用DynamicForm组件 -->
        <DynamicForm
          ref="formRef"
          v-model="formData"
          :config="formConfig"
        />
      </view>
    </view>

    <view class="bottom-actions">
      <wd-button 
        :round="false" 
        type="info" 
        plain 
        custom-class="cancel-btn"
        @click="handleCancel"
      >
        取消
      </wd-button>
      <wd-button 
        :round="false" 
        type="primary" 
        custom-class="submit-btn"
        :loading="isSubmitting"
        @click="handleSubmit"
      >
        提交申请
      </wd-button>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "申请备件"
  }
}
</route>

<style scoped lang="scss">
.apply-spare-part-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 80px;
}

.page-header {
  background-color: #fff;
  padding: 20px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-content {
  .subtitle {
    font-size: 14px;
    color: #666;
  }
}

.form-container {
  padding: 16px;
}

.form-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 12px;
  padding: 12px 16px;
  background-color: #fff;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.cancel-btn {
  flex: 1;
}

.submit-btn {
  flex: 2;
}
</style>
