<script setup lang="ts">
import type { UserChangePasswordParams } from '@/types/api/User'
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types'
import { changePassword } from '@/api'
import { useUserStore } from '@/store'
import { ref } from 'vue'
import { useToast } from 'wot-design-uni'

defineOptions({
  name: 'ModifyPasswordPage',
})

const toast = useToast()
const userStore = useUserStore()
const formRef = ref<FormInstance>()
const formData = ref<Partial<UserChangePasswordParams>>({
  old_password: '',
  password: '',
  confirm_password: '',
})

const formRules: FormRules = {
  old_password: [{ required: true, type: 'string', message: '请输入原密码' }],
  password: [
    { required: true, type: 'string', message: '请输入新密码' },
    { pattern: /^\S{6,}$/, type: 'string', message: '密码长度不能少于6位' },
    { pattern: /^(?=.*[a-z])(?=.*\d).{6,}$/i, type: 'string', message: '密码必须包含字母和数字' },
  ],
  confirm_password: [
    { required: true, type: 'string', message: '请再次输入新密码' },
    {
      type: 'string',
      validator: (value: string) => {
        if (value !== formData.value.password) {
          return Promise.reject(new Error('两次输入的密码不一致'))
        }
        return Promise.resolve()
      },
      message: '两次输入的密码不一致',
    },
  ],
}

const isSubmitting = ref(false)

async function handleModifyPassword() {
  if (isSubmitting.value) return

  const { valid } = await formRef.value!.validate()

  if (valid) {
    isSubmitting.value = true
    try {
      await changePassword({
        ...formData.value,
        loginId: userStore.userInfo?.phone,
      })
      toast.success('修改密码成功')
      uni.navigateTo({ url: '/pages/user/login' })
    } finally {
      isSubmitting.value = false
    }
  }
}

function handleCancel() {
  uni.navigateBack()
}
</script>

<template>
  <view class="forget-password-page h-screen flex flex-col bg-white">
    <view class="form-container flex-1 px-20px pt-20px">
      <wd-form ref="formRef" :model="formData" :rules="formRules" is-border>
        <wd-cell-group border>
          <wd-form-item prop="old_password" label-width="0px">
            <wd-input
              v-model="formData.old_password"
              show-password
              placeholder="请输入原密码"
              clearable
              size="large"
              placeholder-style="font-size: 15px; color: #C0C4CC;"
              input-style="font-size: 15px;"
            />
          </wd-form-item>
          <wd-form-item prop="password" label-width="0px">
            <wd-input
              v-model="formData.password"
              show-password
              placeholder="请输入新密码"
              clearable
              size="large"
              placeholder-style="font-size: 15px; color: #C0C4CC;"
              input-style="font-size: 15px;"
            />
          </wd-form-item>
          <wd-form-item prop="confirm_password" label-width="0px">
            <wd-input
              v-model="formData.confirm_password"
              show-password
              placeholder="请再次输入新密码"
              clearable
              size="large"
              placeholder-style="font-size: 15px; color: #C0C4CC;"
              input-style="font-size: 15px;"
            />
          </wd-form-item>
        </wd-cell-group>
      </wd-form>

      <view class="hint-text mt-15px px-5px text-13px">
        <view>· 密码必须包含字母和数字</view>
        <view class="mt-5px">· 密码长度至少6个字符</view>
        <view class="mt-5px">· 修改密码成功后将自动退出，请使用新密码重新登录</view>
      </view>

      <wd-button
        type="primary"
        block
        size="large"
        custom-class="mt-40px !h-50px"
        :loading="isSubmitting"
        :disabled="isSubmitting"
        :round="false"
        @click="handleModifyPassword"
      >
        {{ isSubmitting ? '修改中...' : '修改' }}
      </wd-button>

      <wd-button
        plain
        block
        size="large"
        :round="false"
        custom-class="mt-15px !h-50px !border-solid !border-1 !border-[#DCDFE6] !text-[#606266]"
        @click="handleCancel"
      >
        取消
      </wd-button>
    </view>
    <view class="h-34px" />
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "修改密码"
  }
}
</route>

<style scoped lang="scss">
.forget-password-page {
  .form-container {
    :deep(.wd-input) {
      padding: 15px 0;
      background-color: transparent;
    }

    :deep(.wd-cell-group) {
      background-color: transparent;

      &::after {
        border: none;
      }
    }

    :deep(.wd-cell) {
      padding: 0 !important; // Keep this if wd-form-item wraps wd-select-picker in a wd-cell
      margin: 0 !important;
      background-color: transparent !important;

      &::after {
        left: 0 !important;
      }
    }

    :deep(.wd-input__label) {
      display: none;
    }

    :deep(.wd-input__suffix) {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }

  .hint-text {
    color: #909399;
  }
}
</style>
