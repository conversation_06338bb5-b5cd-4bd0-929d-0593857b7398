<script setup lang="ts">
const emit = defineEmits(['close', 'agree', 'disagree'])

const visible = defineModel<boolean>('visible', { default: false })

function handleAgree() {
  emit('agree')
}

function handleDisagree() {
  emit('disagree')
}

function goToAgreement(type: 'service' | 'privacy') {
  uni.showToast({ title: `查看${type === 'service' ? '服务协议' : '隐私政策'}`, icon: 'none' })
}
</script>

<template>
  <wd-popup v-model="visible" :close-on-click-modal="false">
    <view class="privacy-dialog">
      <view class="privacy-dialog__title">隐私协议</view>
      <view class="privacy-dialog__content">
        <scroll-view class="content-scroll" scroll-y>
          <view class="content-text">
            <view class="paragraph">
              感谢您使用我们的应用！我们非常重视您的个人信息和隐私保护。为了更好地保障您的权益，在您使用我们的服务前，请您仔细阅读并了解
              <text class="link" @click="goToAgreement('service')">《服务协议》</text>和
              <text class="link" @click="goToAgreement('privacy')">《隐私政策》</text>
              的全部内容。
            </view>
            <view class="paragraph">
              这些协议和政策已经详细列明了我们收集、使用、存储和共享您个人信息的情况，以及您所享有的相关权利。其中包括但不限于：
            </view>
            <view class="paragraph-list">
              1. 我们可能收集的个人信息类型； 2. 我们如何使用这些信息； 3.
              您如何管理自己的个人信息； 4. 我们如何保护您的个人信息安全。
            </view>
            <view class="paragraph">
              您点击"同意"即表示您已阅读并同意上述协议和政策。如您不同意，很遗憾我们将无法为您提供完整的服务。
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="privacy-dialog__footer">
        <wd-button class="btn-disagree" type="info" plain @click="handleDisagree">不同意</wd-button>
        <wd-button class="btn-agree" type="primary" @click="handleAgree">同意</wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
.privacy-dialog {
  display: flex;
  flex-direction: column;
  width: 80vw;
  max-width: 560rpx;
  height: 60vh;
  overflow: hidden;
  background-color: #fff;
  border-radius: 16rpx;

  &__title {
    padding: 30rpx 0;
    font-size: 34rpx;
    font-weight: bold;
    text-align: center;
    border-bottom: 1rpx solid #eee;
  }

  &__content {
    flex: 1;
    padding: 30rpx;
    overflow: hidden;

    .content-scroll {
      height: 100%;

      .content-text {
        font-size: 28rpx;
        line-height: 1.6;
        color: #333;

        .paragraph {
          margin-bottom: 20rpx;
        }

        .paragraph-list {
          padding-left: 20rpx;
          margin-bottom: 20rpx;
        }

        .link {
          display: inline;
          color: #2887ff;
        }
      }
    }
  }

  &__footer {
    display: flex;
    padding: 20rpx 30rpx 30rpx;

    .btn-disagree,
    .btn-agree {
      flex: 1;
      border-radius: 7.5px;
    }

    .btn-disagree {
      min-width: unset;
      margin-right: 20rpx;
      border-color: #d0d3d5;
    }

    .btn-agree {
      min-width: unset;
      font-weight: 500;
    }
  }
}
</style>
