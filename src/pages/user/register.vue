<script setup lang="ts">
import type { RegisterParams } from '@/types/api/User'
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types'
import { registerUser, sendUserRegisterSms } from '@/api'

defineOptions({
  name: 'RegisterPage',
})

const formRef = ref<FormInstance>()
const formData = ref<RegisterParams>({
  mobile: '',
  code: '',
  password: '',
  confirm_password: '',
})

// 新增表单校验规则
const formRules: FormRules = {
  mobile: [
    { required: true, type: 'string', message: '请输入手机号' },
    { type: 'string', pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' },
  ],
  code: [
    { required: true, type: 'string', message: '请输入验证码' },
    { pattern: /^\d{4}$/, type: 'string', message: '验证码必须为4位数字' }, // len for string
  ],
  password: [
    { required: true, type: 'string', message: '请输入密码' },
    { pattern: /^\S{6,}$/, type: 'string', message: '密码长度不能少于6位' }, // min for string length
  ],
  confirm_password: [
    { required: true, type: 'string', message: '请输入确认密码' },
    {
      type: 'string',
      validator: (value: string) => {
        if (value !== formData.value.password) {
          return Promise.reject(new Error('两次输入的密码不一致'))
        }
        return Promise.resolve()
      },
      message: '两次输入的密码不一致',
    },
  ],
}

const isGettingCode = ref(false)
const countdown = ref(60)
const isSubmitting = ref(false)

async function getVerificationCode() {
  if (isGettingCode.value) return
  if (!formData.value.mobile) {
    uni.showToast({ title: '请输入手机号', icon: 'none' })
    return
  }
  isGettingCode.value = true

  try {
    await sendUserRegisterSms({ mobile: formData.value.mobile })
    uni.showToast({ title: '验证码已发送', icon: 'success' })
  } finally {
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
        isGettingCode.value = false
        countdown.value = 60
      }
    }, 1000)
  }
}

async function handleRegister() {
  if (isSubmitting.value) return

  const { valid } = await formRef.value!.validate()

  if (valid) {
    isSubmitting.value = true
    try {
      await registerUser({ ...formData.value })
      uni.showToast({ title: '注册成功', icon: 'success' })
      goToLogin()
    } catch (apiError) {
      console.error('Register API failed:', apiError)
    } finally {
      isSubmitting.value = false
    }
  }
}

function goToLogin() {
  uni.navigateTo({ url: '/pages/user/login' })
}
</script>

<template>
  <view class="register-page h-screen flex flex-col overflow-hidden">
    <view class="register-page__header relative h-300px">
      <view class="bg absolute inset-0 z-0" />
      <view class="logo-container absolute left-30px top-113px z-1">
        <image src="/static/logo.png" class="logo h-72px w-72px rounded-17px" mode="aspectFit" />
      </view>
    </view>

    <!-- Form -->
    <view class="register-page__form mb-8 mt-40px flex-1 px-30px">
      <wd-form ref="formRef" :model="formData" :rules="formRules">
        <wd-cell-group border>
          <wd-form-item prop="mobile" label-width="0px">
            <wd-input
              v-model="formData.mobile"
              placeholder="请输入手机号"
              clearable
              size="large"
              :type="'tel' as any"
              placeholder-style="font-size: 15px; color: #8F959E;"
              input-style="font-size: 15px;"
            />
          </wd-form-item>
          <wd-form-item prop="code" label-width="0px">
            <wd-input
              v-model="formData.code"
              placeholder="请输入验证码"
              clearable
              size="large"
              type="number"
              :maxlength="4"
              placeholder-style="font-size: 15px; color: #8F959E;"
              input-style="font-size: 15px;"
            >
              <template #suffix>
                <wd-button
                  type="text"
                  size="small"
                  :disabled="isGettingCode"
                  custom-class="!text-primary !text-14px !px-0"
                  @click="getVerificationCode"
                >
                  {{ isGettingCode ? `${countdown}s后重试` : '获取验证码' }}
                </wd-button>
              </template>
            </wd-input>
          </wd-form-item>
          <wd-form-item prop="password" label-width="0px">
            <wd-input
              v-model="formData.password"
              show-password
              placeholder="请输入密码"
              clearable
              size="large"
              placeholder-style="font-size: 15px; color: #8F959E;"
              input-style="font-size: 15px;"
            >
            </wd-input>
          </wd-form-item>
          <wd-form-item prop="confirm_password" label-width="0px">
            <wd-input
              v-model="formData.confirm_password"
              show-password
              placeholder="请输入确认密码"
              clearable
              size="large"
              placeholder-style="font-size: 15px; color: #8F959E;"
              input-style="font-size: 15px;"
            >
            </wd-input>
          </wd-form-item>
        </wd-cell-group>
      </wd-form>

      <wd-button
        type="primary"
        block
        size="large"
        custom-class="mt-40px !h-50px !rounded-15px"
        :loading="isSubmitting"
        :disabled="isSubmitting"
        @click="handleRegister"
      >
        {{ isSubmitting ? '注册中...' : '注册' }}
      </wd-button>

      <view class="mt-25px text-center">
        <text class="text-14px text-[#8F959E]"> 已有账号？ </text>
        <text class="text-14px text-primary" @click="goToLogin"> 立即登录 </text>
      </view>
    </view>
    <view class="h-68px" />
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "注册"
  }
}
</route>

<style scoped lang="scss">
.register-page {
  background-color: white;

  &__header {
    .bg {
      background: linear-gradient(103deg, #ffede5 0%, #b9daff 81%);

      &::after {
        position: absolute;
        inset: 0;
        content: '';
        background: linear-gradient(180deg, rgb(255 255 255 / 0.01%) 0%, #fff 95%);
      }
    }

    .logo-container {
      .logo {
        // Actual logo.png is used in template
      }
    }
  }

  &__form {
    :deep(.wd-input) {
      padding: 15px 0;
      background-color: transparent;
    }

    :deep(.wd-cell-group) {
      background-color: transparent;

      &::after {
        border: none;
      }
    }

    :deep(.wd-cell) {
      padding: 0 !important;
      margin: 0 !important;
      background-color: transparent !important;

      &::after {
        left: 0 !important;
        border-bottom: 1px solid #d0d3d5;
      }
    }

    :deep(.wd-cell__left) {
      display: none;
    }

    :deep(.wd-input__label) {
      display: none;
    }

    :deep(.wd-input__suffix) {
      display: flex;
      gap: 12px;
      align-items: center;
    }

    .text-primary {
      color: #2887ff; // Consistent with login page primary color
    }
  }
}
</style>
