<script setup lang="ts">
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
</script>

<template>
  <view class="user-info-page">
    <wd-cell-group border>
      <wd-cell title="姓名" :value="userInfo?.name" />
      <wd-cell title="手机号码" :value="userInfo?.phone" />
      <wd-cell v-if="userInfo?.userType === 'haier'" title="所属部门" :value="userInfo?.dept" />
      <wd-cell v-else title="所属运维商" :value="userInfo?.opName" />
      <wd-cell title="角色" :value="userInfo?.roleName" />
    </wd-cell-group>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "个人信息"
  }
}
</route>

<style scoped lang="scss">
.user-info-page {
  min-height: 100vh;
  background-color: white;
}
</style>
