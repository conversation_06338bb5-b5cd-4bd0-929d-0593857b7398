<script setup lang="ts">
import type { Message, MessagePageParams } from '@/types/api/Message'
import { batchMarkMessageRead, getMessagePage, markMessageRead } from '@/api/message'
import { navigateTo } from '@uni-helper/uni-promises'
import dayjs from 'dayjs'
import { reactive, ref } from 'vue'

const paging = ref<ZPagingInstance | null>(null)
const displayedMessages = ref<(Message & { unread: boolean })[]>([])

const currentTab = ref('SYSTEM')
const tabs = [
  { title: '业务消息', value: 'BUSINESS', messageType: 'BUSINESS' },
  { title: '系统消息', value: 'SYSTEM', messageType: 'SYSTEM' },
]

const queryParams = reactive<MessagePageParams>({
  pageNum: 1,
  pageSize: 20,
  messageType: 'SYSTEM',
})

const fetchDataList = async (pageNo: number, pageSize: number) => {
  queryParams.pageNum = pageNo
  queryParams.pageSize = pageSize
  try {
    const response = await getMessagePage(queryParams)
    const newItems = response.content.map(apiMsg => {
      const existingItem = displayedMessages.value.find(dm => dm.id === apiMsg.id)
      return {
        ...apiMsg,
        unread: existingItem ? existingItem.unread : apiMsg.readStatus === 'UNREAD',
      }
    })
    paging.value?.completeByTotal(newItems, response.totalElements)
  } catch (error) {
    console.error('Failed to fetch message list:', error)
    paging.value?.complete(false)
  }
}

const onTabChange = (event: { name: string }) => {
  queryParams.messageType = event.name
  paging.value?.reload()
}

const handleViewDetail = async (message: Message & { unread: boolean }) => {
  navigateTo({ url: `/pages/message/detail?id=${message.id}` })
  message.unread = false
}

const markAsRead = async (messageId: string) => {
  const message = displayedMessages.value.find(m => String(m.id) === messageId)
  if (message && message.unread) {
    message.unread = false
    await markMessageRead(Number(messageId))
  }
}

const markAllAsRead = async () => {
  const currentTypeMessagesToUpdate = displayedMessages.value.filter(
    dm => dm.messageType === queryParams.messageType && dm.unread,
  )

  if (currentTypeMessagesToUpdate.length === 0) return

  const messageIdsToMarkRead = currentTypeMessagesToUpdate.map(dm => dm.id)

  await batchMarkMessageRead(messageIdsToMarkRead)
  currentTypeMessagesToUpdate.forEach(message => {
    message.unread = false
  })
}
</script>

<template>
  <view class="message-list-page">
    <wd-tabs v-model="currentTab" custom-class="message-tabs" @change="onTabChange">
      <template v-for="item in tabs" :key="item">
        <wd-tab :title="item.title" :name="item.value" />
      </template>
    </wd-tabs>
    <view class="read-all-section">
      <text class="read-all-button" @click="markAllAsRead">一键已读</text>
    </view>

    <z-paging
      ref="paging"
      v-model="displayedMessages"
      class="list-paging-component"
      :fixed="false"
      :default-page-size="queryParams.pageSize"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
      @query="fetchDataList"
    >
      <view class="message-items-container">
        <wd-swipe-action v-for="item in displayedMessages" :key="item.id" :auto-close="true">
          <view class="message-item" @click="handleViewDetail(item)">
            <view class="message-item__status">
              <view v-if="item.unread" class="unread-dot" />
              <view v-else class="read-dot" />
            </view>
            <view class="message-item__content">
              <view class="message-item__header">
                <text class="message-item__title">{{ item.subject }}</text>
              </view>
              <text class="message-item__body">{{ item.createdByName }}</text>
              <text class="message-item__time">{{
                dayjs(item.createdAt).format('YYYY-MM-DD HH:mm')
              }}</text>
            </view>
            <view class="message-item__action">
              <text class="view-detail-text">查看 ></text>
            </view>
          </view>
          <template #right>
            <view style="display: flex; height: 100%">
              <view
                v-if="item.unread"
                style="
                  box-sizing: border-box;
                  display: flex;
                  flex: 1;
                  align-items: center;
                  justify-content: center;
                  height: 100%;
                  padding: 0 15px;
                  font-size: 14px;
                  color: #fff;
                  background-color: #67c23a;
                "
                @click.stop="markAsRead(String(item.id))"
              >
                标为已读
              </view>
            </view>
          </template>
        </wd-swipe-action>
      </view>
    </z-paging>
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "消息通知"
  }
}
</route>

<style scoped lang="scss">
.message-list-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f4f4f5;
}

:deep(.message-tabs) {
  background-color: #fff;
  border-bottom: 0.5px solid #ebeef5;

  .wd-tabs__nav-item {
    font-size: 15px;
    color: #303133;
  }

  .wd-tabs__nav-item.is-active {
    color: #409eff;
  }

  .wd-tabs__line {
    height: 2px;
    background-color: #409eff;
  }
}

.read-all-section {
  padding: 8px 15px;
  background-color: #fff;
  border-bottom: 0.5px solid #ebeef5;
}

.read-all-button {
  font-size: 14px;
  color: #409eff;
}

.list-paging-component {
  flex: 1;
}

.message-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  background-color: #fff;
  border-bottom: 0.5px solid #ebeef5;

  &__status {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    margin-right: 10px;
  }

  .unread-dot {
    width: 8px;
    height: 8px;
    background-color: #f56c6c;
    border-radius: 50%;
  }

  .read-dot {
    width: 8px;
    height: 8px;
    background-color: #39e380;
    border-radius: 50%;
  }

  .read-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border: 1px dashed #909399;
    border-radius: 50%;

    &__text {
      font-size: 10px;
      color: #909399;
    }
  }

  &__content {
    flex: 1;
    margin-right: 10px;
    overflow: hidden;
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;
  }

  &__title {
    max-width: calc(100% - 100px);
    overflow: hidden;
    font-size: 13px;
    font-weight: 500;
    color: #303133;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__time {
    flex-shrink: 0;
    margin-left: 8px;
    font-size: 12px;
    color: #606266;
    white-space: nowrap;
  }

  &__body {
    overflow: hidden;
    font-size: 12px;
    line-height: 1.4;
    color: #909399;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__action {
    flex-shrink: 0;

    .view-detail-text {
      font-size: 13px;
      color: #409eff;
    }
  }
}
</style>
