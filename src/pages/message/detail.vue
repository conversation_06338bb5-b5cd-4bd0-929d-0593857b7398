<script setup lang="ts">
import type { Message } from '@/types/api/Message'
import { getMessageDetail, markMessageRead } from '@/api/message'
import { dayjs } from '@/plugins/dayjs'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'

const messageDetail = ref<Message | null>(null)
const messageId = ref<number | null>(null)

onLoad(async options => {
  if (options?.id) {
    const id = Number(options.id)
    messageId.value = id
    try {
      const detail = await getMessageDetail(id)
      messageDetail.value = detail
      if (detail.status !== 'READ') {
        await markMessageRead(id)
      }
    } catch {
      uni.showToast({
        title: '加载失败',
        icon: 'none',
      })
    }
  } else {
    uni.showToast({
      title: '无效的消息ID',
      icon: 'none',
      complete: () => {
        uni.navigateBack()
      },
    })
  }
})

function formatTime(timeStr: string | undefined, format = 'YYYY-MM-DD HH:mm') {
  if (!timeStr) return ''
  return dayjs(timeStr).format(format)
}
</script>

<template>
  <view class="message-detail-page">
    <template v-if="messageDetail">
      <view class="message-detail-page__header">
        <view class="message-detail-page__subject">
          {{ messageDetail.subject }}
        </view>
        <view class="message-detail-page__meta">
          <text>创建人：{{ messageDetail.createdByName }}</text>
          <text>发布时间：{{ formatTime(messageDetail.sendTime) }}</text>
        </view>
        <view class="message-detail-page__line" />
      </view>

      <view class="message-detail-page__content-wrapper">
        <view class="message-detail-page__html-content">
          <rich-text :nodes="messageDetail.content" />
        </view>
      </view>
    </template>
    <view v-else class="message-detail-page__loading-placeholder">
      <text>加载中...</text>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "通知详情"
  }
}
</route>

<style scoped lang="scss">
.message-detail-page {
  min-height: 100vh;
  padding: 16px;
  background-color: #fff;
}

.message-detail-page__header {
  margin-bottom: 16px;
}

.message-detail-page__subject {
  margin-bottom: 8px;
  font-size: 20px;
  font-weight: bold;
  color: #2c3e50;
}

.message-detail-page__meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  font-size: 14px;
  color: #7f8c8d;
}

.message-detail-page__line {
  height: 1px;
  background-color: #edf2f7;
}

.message-detail-page__content-wrapper {
  background-color: #fff;
}

.message-detail-page__html-content {
  font-size: 15px;
  line-height: 1.6;
  color: #34495e;
  word-break: break-all;

  // Rich text styles might need global styling if :deep doesn't work as expected
  // or if specific tags need styling.
  // For example:
  // :deep(p) {
  //   margin-bottom: 10px;
  // }
  // :deep(img) {
  //   max-width: 100%;
  //   height: auto;
  // }
}

.message-detail-page__loading-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
}
</style>
