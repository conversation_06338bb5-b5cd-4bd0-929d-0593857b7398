<script setup lang="ts">
import type { StationRent } from '@/types/api/Station'
import { getStationRentByStationCode } from '@/api/station'
import { onLoad } from '@dcloudio/uni-app'
import { makePhoneCall } from '@uni-helper/uni-promises'

import dayjs from 'dayjs'
import { computed, ref } from 'vue'

const stationCode = ref('')
const stationName = ref('')
const stationRentDetail = ref<StationRent | null>(null)
const selectedDate = ref(new Date().getTime())

const formattedMonthForDisplay = computed(() => dayjs(selectedDate.value).format('YYYY年MM月'))
const formattedMonthForApi = computed(() => dayjs(selectedDate.value).format('YYYY-MM'))

async function fetchData() {
  if (!stationCode.value)
    return
  try {
    const res = await getStationRentByStationCode({
      stationCode: stationCode.value,
      month: formattedMonthForApi.value,
    })
    stationRentDetail.value = res
  }
  catch (error) {
    console.error('Failed to fetch station rent detail:', error)
    stationRentDetail.value = null
  }
}

function handleConfirm({ value }: { value: number }) {
  selectedDate.value = value
  fetchData()
}

onLoad((options) => {
  if (options?.stationCode) {
    stationCode.value = options.stationCode
    stationName.value = options.stationName || ''
    fetchData()
  }
})

function makePhoneCallHandler() {
  if (stationRentDetail.value?.phone) {
    makePhoneCall({
      phoneNumber: stationRentDetail.value.phone,
    })
  }
}
</script>

<template>
  <view class="rent-detail">
    <view v-if="stationRentDetail" class="info-section">
      <view class="info-item">
        <text class="info-item__label">电站编码</text>
        <text class="info-item__value">{{ stationCode }}</text>
      </view>
      <view class="info-item">
        <text class="info-item__label">电站名称</text>
        <text class="info-item__value">{{ stationRentDetail.tenantName }}</text>
      </view>
      <view class="info-item">
        <text class="info-item__label">联系方式</text>
        <text class="info-item__value info-item__value--link" @click="makePhoneCallHandler">
          {{ stationRentDetail.phone }}
        </text>
      </view>
      <view class="info-item">
        <text class="info-item__label">电站地址</text>
        <text class="info-item__value">{{ stationRentDetail.stationAddress }}</text>
      </view>
      <view class="info-item info-item--vertical">
        <text class="info-item__label">电站照片</text>
        <view class="info-item__image-container">
          <image
            v-if="stationRentDetail.stationImg"
            class="info-item__image"
            :src="stationRentDetail.stationImg"
            mode="aspectFill"
          />
          <view v-else class="info-item__image-placeholder">
            暂无照片
          </view>
        </view>
      </view>
      <view class="info-item">
        <text class="info-item__label">请选择月份</text>
        <wd-calendar v-model="selectedDate" type="month" @confirm="handleConfirm">
          <view class="month-display">
            {{ formattedMonthForDisplay }}
            <wd-icon name="arrow-down" />
          </view>
        </wd-calendar>
      </view>
      <view class="info-item">
        <text class="info-item__label">项目公司</text>
        <text class="info-item__value">{{ stationRentDetail.projectCompanyName }}</text>
      </view>
      <view class="info-item">
        <text class="info-item__label">发电户号</text>
        <text class="info-item__value">{{ stationRentDetail.elecNo }}</text>
      </view>
      <view class="info-item">
        <text class="info-item__label">发电量</text>
        <text class="info-item__value">{{ stationRentDetail.elec }}/KWh</text>
      </view>
      <view class="info-item">
        <text class="info-item__label">电费单价</text>
        <text class="info-item__value">{{ stationRentDetail.elecPrice }}元/KWh</text>
      </view>
      <view class="info-item">
        <text class="info-item__label">实收电费</text>
        <text class="info-item__value">{{ stationRentDetail.paymentMoney }}元</text>
        <!-- <view class="status-badge" :class="paymentStatusInfo.class">
          <text class="status-badge__text">{{ paymentStatusInfo.text }}</text>
        </view> -->
      </view>
      <view class="info-item info-item--last">
        <text class="info-item__label">银行卡号</text>
        <text class="info-item__value">{{ stationRentDetail.bankCardNumber }}</text>
      </view>
    </view>
    <view v-else class="loading-state">
      <wd-loading />
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "租金详情"
  }
}
</route>

<style scoped lang="scss">
.rent-detail {
  min-height: 100vh;
  padding-bottom: calc(env(safe-area-inset-bottom));
  background-color: #f7f8fa;
}

.info-section {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px;
  font-size: 15px;
  border-bottom: 1px solid #f2f2f2;

  &--vertical {
    align-items: flex-start;
  }

  &--last {
    border-bottom: none;
  }

  &__label {
    flex-shrink: 0;
    min-width: 70px;
    margin-right: 10px;
    color: #91929e;
  }

  &__value {
    flex-grow: 1;
    color: #4b4b4b;
    text-align: right;
    word-break: break-all;

    &--link {
      color: #409eff;
    }
  }

  &__image-container {
    width: 100px;
    height: 100px;
    overflow: hidden;
    border-radius: 12px;
  }

  &__image {
    width: 100%;
    height: 100%;
  }

  &__image-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #f7f8fa;
    color: #c8c9cc;
    font-size: 14px;
  }

  :deep(.wd-calendar) {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: auto;
    max-width: calc(100% - 80px);
    height: 36px;
    padding: 8px 14px;
    border: 1px dashed #dee2e6;
    border-radius: 4px;
    overflow: hidden;
  }
}

.month-display {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #4b4b4b;
}

.status-badge {
  padding: 2px 10px;
  margin-left: 10px;
  border-radius: 12px;
  border: 1px solid;
  flex-shrink: 0;

  &--paid {
    background-color: #f0f9eb;
    border-color: #e1f3d8;
    .status-badge__text {
      color: #67c23a;
    }
  }

  &--unpaid {
    background-color: #fef0f0;
    border-color: #fde2e2;
    .status-badge__text {
      color: #f56c6c;
    }
  }

  &--unknown {
    background-color: #f4f4f5;
    border-color: #e9e9eb;
    .status-badge__text {
      color: #909399;
    }
  }

  &__text {
    font-size: 12px;
  }
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 40px;
}
</style>
