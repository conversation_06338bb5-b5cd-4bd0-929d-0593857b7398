<script setup lang="ts">
import type { GeneratePracticeRequest } from '@/types/api/Exam'
import {
  generatePracticeQuestions,
  getAnswerDetail,
  getExamUserPage,
  getQuestionBankList,
} from '@/api/exam'
import { UserExamStatus } from '@/types/enums'
import { onShow } from '@dcloudio/uni-app'
import { navigateTo } from '@uni-helper/uni-promises'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import { onMounted, ref } from 'vue'
import { useToast } from 'wot-design-uni'

dayjs.extend(duration)
const toast = useToast()

interface StatCard {
  id: number
  title: string
  icon: string
  bgColor: string
  path: string
}

interface OngoingExam {
  id: number
  answerId: number
  title: string
  // timeLeft: string
  totalQuestions: number
  completedQuestions: number
}

interface QuestionBankItem {
  id?: number
  title?: string
  svgString?: string
  iconBgColor?: string
  details?: string
  actionText?: string
  totalCount?: number
}

const statCards = ref<StatCard[]>([
  {
    id: 1,
    title: '我的考试',
    icon: 'i-carbon-edit', // Placeholder, replace with actual icon
    bgColor: 'background-gradient-exams',
    path: '/pages/training-center/exam/list',
  },
  {
    id: 2,
    title: '练习记录',
    icon: 'i-carbon-bookmark', // Placeholder, replace with actual icon
    bgColor: 'background-gradient-practice',
    path: '/pages/training-center/practice/list',
  },
])

const ongoingExam = ref<OngoingExam | null>(null)

const iconPresets = [
  {
    svgString: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M17.9011 2.398C17.9878 2.41355 18.0767 2.38659 18.1402 2.3255L18.5373 1.94258C18.6366 1.83529 18.6349 1.66917 18.5335 1.56388C18.4321 1.45859 18.2662 1.45071 18.1552 1.54592L17.7581 1.92842C17.6838 2.00039 17.656 2.10789 17.6862 2.20686C17.7163 2.30583 17.7992 2.37963 17.9011 2.398ZM19.1302 4.92467C19.1587 4.92969 19.1878 4.93025 19.2165 4.92633L19.7623 4.85008C19.9105 4.82623 20.0124 4.68827 19.9916 4.53962C19.9709 4.39097 19.8351 4.2862 19.6861 4.30383L19.1402 4.38008C19.0061 4.39912 18.9057 4.5127 18.9032 4.64811C18.9007 4.78353 18.9969 4.90072 19.1302 4.92467ZM19.3036 7.43633L18.8173 7.17717C18.683 7.1056 18.5162 7.15643 18.4446 7.29071C18.373 7.42498 18.4239 7.59185 18.5581 7.66342L19.0444 7.92258C19.179 7.99342 19.3452 7.94258 19.4177 7.80925C19.4886 7.6747 19.4376 7.50812 19.3036 7.43633ZM10.7931 2.89883L10.3065 2.63967C10.1736 2.57822 10.0159 2.63123 9.94716 2.76044C9.8784 2.88965 9.92254 3.05006 10.0477 3.12592L10.534 3.3855C10.6685 3.45592 10.8348 3.40508 10.9073 3.27175C10.9779 3.1373 10.927 2.97103 10.7931 2.89925L10.7931 2.89883ZM16.9115 9.24967C16.8415 9.11918 16.681 9.06719 16.5478 9.13188C16.4147 9.19656 16.3563 9.35485 16.4156 9.4905L16.6565 9.98633C16.7192 10.1299 16.8888 10.1924 17.0297 10.1239C17.1706 10.0555 17.2263 9.88357 17.1523 9.7455L16.9115 9.24967ZM12.4385 1.31342C12.5023 1.45539 12.6709 1.51651 12.8108 1.44841C12.9508 1.38031 13.0067 1.20998 12.9344 1.07217L12.6935 0.576334C12.6243 0.444359 12.4627 0.391297 12.3286 0.45651C12.1946 0.521724 12.1366 0.681621 12.1977 0.817584L12.4385 1.31342ZM15.4219 1.07592C15.5719 1.1029 15.7152 1.00273 15.7415 0.852584L15.8373 0.310084C15.8558 0.164469 15.7568 0.0299671 15.6123 0.00428767C15.4677 -0.0213918 15.3285 0.0707751 15.2956 0.213834L15.199 0.756334C15.172 0.906345 15.2722 1.04969 15.4223 1.07592L15.4219 1.07592ZM16.0819 14.4509L14.054 5.69258C13.9165 5.09175 13.3256 4.62008 12.734 4.62008L3.41563 4.62008C2.82397 4.62008 2.23313 5.09175 2.09563 5.69217L0.0677161 14.4509C-0.192701 15.5897 0.322299 16.3543 1.19563 16.3543L14.954 16.3543C15.8273 16.3543 16.3423 15.5901 16.0819 14.4509ZM15.204 15.3301C15.0915 15.4688 14.9248 15.5384 14.7081 15.5384L1.4423 15.5384C1.22605 15.5384 1.05897 15.4688 0.946466 15.3301C0.773549 15.118 0.731466 14.753 0.830216 14.3301L2.78605 6.07425C2.8673 5.72758 3.23188 5.43508 3.58313 5.43508L12.5673 5.43508C12.9181 5.43508 13.2831 5.728 13.3644 6.07592L15.3198 14.3288C15.4186 14.753 15.3761 15.118 15.2031 15.3301L15.204 15.3301Z" fill="#F6B60A"/><path fill-rule="evenodd" clip-rule="evenodd" d="M5.62705 4.44292L2.43872 4.44292C2.04872 4.44292 1.66789 4.74125 1.58914 5.11625L1.13497 7.28875C1.03914 7.75042 1.32664 8.14042 1.77872 8.14042L5.4758 8.14042C5.92789 8.14042 6.30122 7.75042 6.30622 7.28875L6.32914 5.11625C6.3333 4.74083 6.01705 4.44292 5.62705 4.44292ZM7.49789 8.14042L11.1687 8.14042C11.6179 8.14042 11.9037 7.75042 11.8079 7.28875L11.3562 5.11625C11.2783 4.74083 10.9 4.44292 10.5125 4.44292L7.3458 4.44292C6.95913 4.44292 6.6458 4.74125 6.64997 5.11625L6.67288 7.28875C6.67788 7.75042 7.04872 8.14042 7.49789 8.14042ZM5.45914 8.42167L1.70914 8.42167C1.25039 8.42167 0.788719 8.835 0.679136 9.3575L0.032469 12.4462C-0.107948 13.1154 0.219969 13.6854 0.767052 13.6854L5.24247 13.6854C5.78913 13.6854 6.24289 13.1162 6.25038 12.4462L6.28288 9.3575C6.2883 8.835 5.91788 8.42167 5.45914 8.42167ZM12.26 9.3575C12.1508 8.835 11.6925 8.42167 11.2371 8.42167L7.51372 8.42167C7.0583 8.42167 6.69038 8.835 6.69622 9.3575L6.72872 12.4462C6.73622 13.1154 7.18622 13.6854 7.72913 13.6854L12.1729 13.6854C12.7162 13.6854 13.0421 13.1162 12.9025 12.4462L12.26 9.3575ZM8.7058 16.5804L7.14747 16.5804L7.14747 15.9746C7.14747 15.64 6.87623 15.3687 6.54163 15.3687C6.20704 15.3687 5.9358 15.64 5.9358 15.9746L5.9358 16.5804L4.37663 16.5804C4.06663 16.5804 3.81497 16.8312 3.81497 17.1417L3.81497 17.2308C3.81497 17.5408 4.06622 17.7925 4.37663 17.7925L8.7058 17.7925C9.01622 17.7925 9.26789 17.5408 9.26789 17.2308L9.26789 17.1417C9.26789 16.8317 9.01622 16.5804 8.70622 16.5804L8.7058 16.5804ZM13.0675 0.000416565C11.4085 0.00113564 9.93594 1.06263 9.4108 2.63625L11.7621 2.63625C12.3537 2.63625 12.9446 3.1075 13.0821 3.70833L13.9837 7.60167C15.6712 7.19042 16.9254 5.6725 16.9254 3.85833C16.9254 1.72743 15.1983 9.53674e-08 13.0679 0L13.0675 0.000416565Z" fill="#F6B60A"/></svg>`,
    iconBgColor: '#FFE8AA',
  },
  {
    svgString: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16.6113 10.6523C16.5039 10.6523 16.3965 10.6738 16.2988 10.7168L16.2949 10.7188L16.291 10.7188L8.71484 13.9473L1.13672 10.7207L1.13281 10.7207L1.12891 10.7188C1.03125 10.6777 0.927734 10.6543 0.816406 10.6543C0.365234 10.6543 0 11.0312 0 11.4961C0 11.8477 0.208984 12.1465 0.503906 12.2734L8.39258 15.6348L8.40039 15.6367C8.59961 15.7227 8.82617 15.7227 9.02539 15.6367L9.0293 15.6348C9.03125 15.6348 9.03125 15.6328 9.0332 15.6328L16.9219 12.2715C17.2168 12.1445 17.4258 11.8457 17.4258 11.4941C17.4277 11.0293 17.0625 10.6523 16.6113 10.6523ZM16.6113 7.00977Q16.457 7.00586 16.2988 7.07227L8.71484 10.3027L1.12891 7.07227Q0.927734 7.00977 0.816406 7.00977C0.365234 7.00977 0 7.38477 0 7.84961C0 8.19922 0.208984 8.5 0.503906 8.625L8.39258 11.9844C8.39453 11.9844 8.39453 11.9863 8.39648 11.9863L8.39844 11.9883C8.49609 12.0293 8.59961 12.0527 8.71094 12.0527C8.82227 12.0527 8.92773 12.0293 9.02344 11.9883L9.02734 11.9863C9.0293 11.9863 9.0293 11.9863 9.03125 11.9844L16.9199 8.625C17.2148 8.5 17.4238 8.19922 17.4258 7.84961C17.4277 7.38477 17.0625 7.00977 16.6113 7.00977ZM0.503906 4.98242L8.39258 8.34375C8.39453 8.34375 8.39453 8.34375 8.39648 8.3457L8.39844 8.34766C8.49609 8.38867 8.59961 8.41016 8.71094 8.41016C8.81836 8.41016 8.92383 8.38867 9.02344 8.34766L9.02734 8.3457L9.03125 8.34375L16.9199 4.98438C17.2148 4.85742 17.4238 4.55859 17.4258 4.20898C17.4258 3.85742 17.2168 3.55859 16.9219 3.43164L9.0332 0.0683594C9.03125 0.0683594 9.03125 0.0683594 9.0293 0.0664062L9.02539 0.0644531C8.82617 -0.0214844 8.59961 -0.0214844 8.40039 0.0644531L8.39844 0.0664062L0.503906 3.42969C0.208984 3.55469 0 3.85547 0 4.20703C0 4.55664 0.208984 4.85547 0.503906 4.98242Z" fill="#74CB3B"/></svg>`,
    iconBgColor: '#C7F9A5',
  },
  {
    svgString: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16.5625 2.65951L0.9375 2.65951C0.421875 2.65951 0 3.05801 0 3.54508L0 14.7622C0 15.2493 0.421875 15.6478 0.9375 15.6478L16.5625 15.6478C17.0781 15.6478 17.5 15.2493 17.5 14.7622L17.5 3.54508C17.5 3.05801 17.0781 2.65951 16.5625 2.65951ZM7.74932 7.37023L1.57324 7.37023L1.57324 6.00559L7.74932 6.00559L7.74932 7.37023ZM15.8913 11.6629C15.4157 12.4893 14.7052 12.9445 13.8908 12.9445C13.2345 12.9445 12.7186 12.7157 12.166 12.1794C11.858 11.8847 11.656 11.7532 11.2744 11.7532C10.9073 11.7532 10.5619 11.9987 10.3266 12.4266C10.2562 12.5546 10.1044 12.6031 9.97904 12.5376L9.39017 12.2302C9.32308 12.1952 9.27299 12.1318 9.25215 12.0557C9.23129 11.9796 9.2416 11.8977 9.28055 11.83C9.75623 11.0035 10.4667 10.5483 11.2811 10.5483C11.9374 10.5483 12.4532 10.7771 13.0058 11.3133C13.3139 11.6081 13.5158 11.7396 13.8975 11.7396C14.2646 11.7396 14.61 11.4942 14.8453 11.0663C14.9157 10.9383 15.0674 10.8898 15.1929 10.9553L15.7817 11.2627C15.8488 11.2977 15.8989 11.361 15.9197 11.4371C15.9406 11.5133 15.9303 11.5952 15.8913 11.6629ZM6.24023 1.7334L6.24023 0.46875C6.24023 0.210938 6.0293 0 5.77148 0L2.96875 0C2.71094 0 2.5 0.210938 2.5 0.46875L2.5 1.7334L6.24023 1.7334ZM14.972 1.75619L14.972 0.491543C14.972 0.23373 14.7611 0.0227928 14.5033 0.0227928L11.7005 0.0227928C11.4427 0.0227928 11.2318 0.23373 11.2318 0.491543L11.2318 1.75619L14.972 1.75619Z" fill="#3EA2FF"/></svg>`,
    iconBgColor: '#CBE6FF',
  },
]
const questionBanks = ref<QuestionBankItem[]>([])

async function fetchOngoingExam() {
  try {
    const examResponse = await getExamUserPage({
      pageNum: 1,
      pageSize: 1,
      userExamStatus: UserExamStatus.IN_PROGRESS,
    })

    const exam = examResponse.content?.[0]

    if (exam && exam.id && exam.answerId) {
      const answerDetail = await getAnswerDetail({ answerId: exam.answerId })

      const completedQuestions = Object.keys(answerDetail.answers || {}).length
      const totalQuestions = answerDetail.questions?.length || exam.questionCount || 0

      ongoingExam.value = {
        id: exam.id,
        answerId: exam.answerId,
        title: exam.name || '进行中的考试',
        // timeLeft,
        totalQuestions,
        completedQuestions,
      }
    } else {
      ongoingExam.value = null
    }
  } catch (error) {
    console.error('Failed to fetch ongoing exam:', error)
    ongoingExam.value = null
  }
}

async function fetchQuestionBanks() {
  try {
    const data = await getQuestionBankList({})
    questionBanks.value = data.map((bank, index) => {
      const preset = iconPresets[index % iconPresets.length]
      return {
        id: bank.id,
        title: bank.name,
        details: `总题数：${bank.totalCount || 0}题`,
        actionText: '练习',
        svgString: preset.svgString,
        iconBgColor: preset.iconBgColor,
        totalCount: bank.totalCount,
      }
    })
  } catch (error) {
    console.error('Failed to fetch question banks:', error)
    toast.error('加载题库失败')
  }
}

function navigateToExam(exam: OngoingExam) {
  if (exam.answerId) {
    navigateTo({
      url: `/pages/training-center/exam/exam?answerId=${exam.answerId}`,
    })
  }
}

async function navigateToPractice(bank: QuestionBankItem) {
  if (!bank.id) {
    toast.error('题库信息不完整')
    return
  }

  if (!bank.totalCount) {
    toast.warning('题库中没有题目，请添加题目')
    return
  }

  try {
    toast.loading('生成练习中...')

    // 调用生成练习题接口
    const practiceRequest: GeneratePracticeRequest = {
      bankIds: [bank.id],
      randomOrder: true, // 默认随机排序
    }

    const result = await generatePracticeQuestions(practiceRequest)

    toast.close()

    // 跳转到练习详情页面
    navigateTo({
      url: `/pages/training-center/practice/practice?practiceId=${result.practiceId}`,
    })
  } catch (error) {
    toast.close()
    console.error('生成练习失败:', error)
    toast.error('生成练习失败，请重试')
  }
}

function goToExamList(url: string) {
  navigateTo({ url })
}

onShow(() => {
  fetchOngoingExam()
})

onMounted(() => {
  fetchQuestionBanks()
})
</script>

<template>
  <view class="training-center-page">
    <!-- 统计卡片 -->
    <view class="stats-cards">
      <view
        v-for="card in statCards"
        :key="card.id"
        class="stats-cards__item"
        :class="[card.bgColor]"
        @click="goToExamList(card.path)"
      >
        <view class="stats-cards__icon-wrapper">
          <text :class="card.icon" class="stats-cards__icon" />
        </view>
        <view class="stats-cards__content">
          <text class="stats-cards__title">{{ card.title }}</text>
        </view>
      </view>
    </view>

    <!-- 进行中的考试 -->
    <view v-if="ongoingExam" class="ongoing-exam">
      <view class="section-header">
        <text class="section-title">进行中的考试</text>
      </view>
      <view class="ongoing-exam__card">
        <view class="exam-card-main">
          <view class="exam-card-info">
            <text class="exam-title">{{ ongoingExam.title }}</text>
            <!-- <text class="exam-timeleft">剩余时间：{{ ongoingExam.timeLeft }}</text> -->
          </view>
          <view class="exam-status-badge">
            <text class="exam-status-text">进行中</text>
          </view>
        </view>
        <view class="exam-card-progress-section">
          <view class="progress-bar-wrapper">
            <view class="progress-bar-background">
              <view
                class="progress-bar-fill"
                :style="{
                  width: `${(ongoingExam.completedQuestions / ongoingExam.totalQuestions) * 100}%`,
                }"
              />
            </view>
          </view>
          <text class="progress-text">
            已完成 {{ ongoingExam.completedQuestions }}/{{ ongoingExam.totalQuestions }} 题
          </text>
          <wd-button
            type="primary"
            custom-class="continue-exam-button"
            size="small"
            :round="false"
            @click="navigateToExam(ongoingExam)"
          >
            继续考试
          </wd-button>
        </view>
        <image src="/static/workbench/icon-8.svg" class="exam-card-decoration" mode="aspectFit" />
      </view>
    </view>

    <!-- 全部题库 -->
    <view class="question-banks-section">
      <view class="section-header">
        <text class="section-title">全部题库</text>
      </view>
      <view class="question-bank-list">
        <view
          v-for="bank in questionBanks"
          :key="bank.id"
          class="question-bank-card"
          @click="navigateToPractice(bank)"
        >
          <view class="question-bank-icon-wrapper" :style="{ backgroundColor: bank.iconBgColor }">
            <view class="question-bank-icon" v-html="bank.svgString" />
          </view>
          <view class="question-bank-info">
            <text class="question-bank-title">{{ bank.title }}</text>
            <text class="question-bank-details">{{ bank.details }}</text>
          </view>
          <wd-button
            custom-class="practice-button"
            :round="false"
            plain
            type="primary"
            size="small"
          >
            {{ bank.actionText }}
          </wd-button>
        </view>
      </view>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "培训中心",
    "navigationStyle": "default"
  }
}
</route>

<style scoped lang="scss">
.training-center-page {
  box-sizing: border-box;
  min-height: 100vh;
  padding: 15px;
  background-color: #f7f7f5;
}

.background-gradient-exams {
  background: linear-gradient(135deg, #93c5fd, #67e8f9);
}

.background-gradient-practice {
  background: linear-gradient(135deg, #5eead4, #6ee7b7);
}

.stats-cards {
  display: flex;
  gap: 15px;
  justify-content: space-between;
  margin-bottom: 15px;

  &__item {
    box-sizing: border-box;
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: space-between;
    height: 100px;
    padding: 14px;
    color: #fff;
    border-radius: 10px;
  }

  &__icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 26px;
    height: 26px;
    margin-bottom: 10px;
    background-color: rgb(255 255 255 / 20%);
    border-radius: 50%;
  }

  &__icon {
    font-size: 16px;
    color: #fff;
  }

  &__content {
    display: flex;
    flex-direction: column;
  }

  &__title {
    font-size: 15px;
    font-weight: 500;
    line-height: 24px;
  }
}

.section-header {
  margin-bottom: 10px;
}

.section-title {
  font-size: 15px;
  font-weight: 500;
  color: #4b4b4b;
}

.ongoing-exam {
  position: relative;
  padding: 16px;
  margin-bottom: 15px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 10px;

  &__card {
    padding: 14px;
    background-color: #eff6ff;
    border-radius: 7px;
  }
}

.exam-card-main {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 10px;
}

.exam-card-info {
  display: flex;
  flex-direction: column;
}

.exam-title {
  margin-bottom: 5px;
  font-size: 14px;
  font-weight: 500;
  color: #409eff;
}

.exam-timeleft {
  font-size: 12px;
  color: #91929e;
}

.exam-status-badge {
  padding: 3px 9px;
  background-color: rgb(55 172 254 / 10%);
  border-radius: 9999px;
}

.exam-status-text {
  font-size: 12px;
  color: #409eff;
}

.exam-card-progress-section {
  display: flex;
  gap: 10px;
  align-items: center;
}

.progress-bar-wrapper {
  flex-grow: 1;
}

.progress-bar-background {
  height: 6px;
  overflow: hidden;
  background-color: rgb(55 172 254 / 10%);
  border-radius: 8px;
}

.progress-bar-fill {
  height: 100%;
  background-color: #37acfe;
  border-radius: 8px;
}

.progress-text {
  font-size: 10.5px;
  color: #91929e;
  white-space: nowrap;
}

.continue-exam-button {
  height: 32px !important;
  padding: 0 15px !important;
  font-size: 14px !important;

  --wd-button-br: 4px !important;
}

.exam-card-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 70px;
  height: 70px;
  opacity: 0.1;
}

.question-bank-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.question-bank-card {
  display: flex;
  gap: 15px;
  align-items: center;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
}

.question-bank-icon-wrapper {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.question-bank-icon {
  display: flex; // To center SVG if its viewbox is larger
  align-items: center;
  justify-content: center;
  width: 20px; // Match SVG width
  height: 20px; // Match SVG height
}

.question-bank-icon > :deep(svg) {
  // Style the SVG rendered by v-html
  width: 100%;
  height: 100%;
}

.question-bank-info {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
}

.question-bank-title {
  margin-bottom: 5px;
  font-size: 15px;
  color: #4b4b4b;
}

.question-bank-details {
  font-size: 13px;
  color: #91929e;
}

.practice-button {
  height: 26px !important;
  padding: 0 10px !important;
  font-size: 12px !important;

  --wd-button-br: 13px !important;
  --wd-button-plain-color: #409eff !important;
  --wd-button-plain-border-color: #409eff !important; // For plain primary
}
</style>
