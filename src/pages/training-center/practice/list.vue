<script setup lang="ts">
import type { Practice } from '@/types/api/Exam'
import { deletePractice, getUserPractices } from '@/api/exam'
import { onShow } from '@dcloudio/uni-app'
import { navigateTo } from '@uni-helper/uni-promises'
import dayjs from 'dayjs'
import { ref } from 'vue'

const practiceList = ref<Practice[]>([])
const loading = ref(false)

// 获取练习列表
const fetchPracticeList = async () => {
  try {
    loading.value = true
    const result = await getUserPractices()
    practiceList.value =
      result.map(item => {
        return {
          ...item,
          bankNames: JSON.parse(item.bankNames || '[]')[0],
        }
      }) || []
  } catch (error) {
    console.error('获取练习列表失败:', error)
    uni.showToast({
      title: '获取练习列表失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

const formatTime = (timeStr?: string) => {
  if (!timeStr) return '--'
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm:ss')
}

// 获取练习状态文本和样式
const getPracticeStatus = (practice: Practice) => {
  if (practice.status === 'COMPLETED') {
    return { text: '已完成', type: 'completed' }
  }

  return { text: '进行中', type: 'in-progress' }
}

// 点击练习项
const handlePracticeClick = async (practice: Practice) => {
  // 直接跳转到练习界面开始练习
  navigateTo({
    url: `/pages/training-center/practice/practice?practiceId=${practice.id}`,
  })
}

// 删除练习
const handleDeletePractice = async (practice: Practice) => {
  if (!practice.id) return

  try {
    await deletePractice({ practiceId: practice.id })
    uni.showToast({
      title: '删除成功',
      icon: 'success',
    })
    // 重新获取列表
    fetchPracticeList()
  } catch (error) {
    console.error('删除练习失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'none',
    })
  }
}

onShow(() => {
  fetchPracticeList()
})
</script>

<template>
  <view class="practice-list-page">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 练习列表 -->
    <view v-else-if="practiceList.length > 0" class="practice-list__content">
      <wd-swipe-action v-for="practice in practiceList" :key="practice.id" :auto-close="true">
        <view class="practice-list__card" @click="handlePracticeClick(practice)">
          <view class="practice-list__header">
            <view class="practice-list__title-section">
              <text class="practice-list__title">{{ practice.bankNames || '练习题目' }}</text>
            </view>
            <wd-tag
              :type="getPracticeStatus(practice).type === 'completed' ? 'success' : 'primary'"
              size="small"
              custom-class="practice-list__status-tag"
            >
              {{ getPracticeStatus(practice).text }}
            </wd-tag>
          </view>

          <view class="practice-list__body">
            <view class="practice-list__detail-row">
              <text class="practice-list__label">题目数量</text>
              <text class="practice-list__value">{{ practice.totalCount || 0 }}题</text>
            </view>
            <view class="practice-list__detail-row">
              <text class="practice-list__label">已答题数</text>
              <text class="practice-list__value">{{ practice.answeredCount || 0 }}题</text>
            </view>
            <view class="practice-list__detail-row">
              <text class="practice-list__label">开始时间</text>
              <text class="practice-list__value">{{ formatTime(practice.startTime) }}</text>
            </view>
            <view class="practice-list__detail-row">
              <text class="practice-list__label">最后更新</text>
              <text class="practice-list__value">{{ formatTime(practice.lastUpdateTime) }}</text>
            </view>
          </view>

          <!-- 进度条 -->
          <view v-if="practice.status === 'IN_PROGRESS'" class="practice-list__progress">
            <view class="practice-list__progress-info">
              <text class="practice-list__progress-label">答题进度</text>
              <text class="practice-list__progress-text"
                >{{ practice.answeredCount || 0 }}/{{ practice.totalCount || 0 }}</text
              >
            </view>
            <view class="practice-list__progress-bar">
              <view
                class="practice-list__progress-fill"
                :style="{
                  width: `${((practice.answeredCount || 0) / (practice.totalCount || 1)) * 100}%`,
                }"
              />
            </view>
          </view>
        </view>

        <template #right>
          <view class="swipe-actions">
            <view class="delete-action" @click.stop="handleDeletePractice(practice)">
              <text class="i-carbon-trash-can delete-icon" />
              <text class="delete-text">删除</text>
            </view>
          </view>
        </template>
      </wd-swipe-action>
    </view>

    <!-- 空状态 -->
    <view v-else class="empty-container">
      <text class="empty-text">暂无练习记录</text>
      <text class="empty-desc">去培训中心开始练习吧</text>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "练习记录"
  }
}
</route>

<style scoped lang="scss">
.practice-list-page {
  box-sizing: border-box;
  min-height: 100vh;
  padding: 16px;
  background-color: #f7f7f5;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.swipe-actions {
  display: flex;
  height: 100%;
}

.delete-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  padding: 0 20px;
  color: #fff;
  background-color: #ff4d4f;
}

.delete-icon {
  margin-bottom: 4px;
  font-size: 20px;
  color: #fff;
}

.delete-text {
  font-size: 12px;
  color: #fff;
}

.practice-list {
  &__content {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &__card {
    padding: 16px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
    transition: all 0.3s ease;

    &:active {
      opacity: 0.8;
      transform: scale(0.98);
    }
  }

  &__header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 12px;
  }

  &__title-section {
    flex: 1;
  }

  &__title {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    color: #333;
  }

  &__status-tag {
    flex-shrink: 0;
  }

  &__body {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  &__detail-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__label {
    font-size: 14px;
    color: #666;
  }

  &__value {
    font-size: 14px;
    font-weight: 500;
    color: #333;
  }

  &__progress {
    padding-top: 12px;
    margin-top: 12px;
    border-top: 1px solid #f0f0f0;

    &-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
    }

    &-label {
      font-size: 14px;
      color: #666;
    }

    &-text {
      font-size: 14px;
      font-weight: 500;
      color: #1890ff;
    }

    &-bar {
      height: 6px;
      overflow: hidden;
      background-color: #f0f0f0;
      border-radius: 3px;
    }

    &-fill {
      height: 100%;
      background-color: #1890ff;
      border-radius: 3px;
      transition: width 0.3s ease;
    }
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  color: #666;
}

.empty-desc {
  font-size: 14px;
  color: #999;
}
</style>
