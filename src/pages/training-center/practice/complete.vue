<script setup lang="ts">
import type { PracticeResult } from '@/types/api/Exam'
import { getPracticeDetail } from '@/api/exam'
import { onLoad } from '@dcloudio/uni-app'
import { computed, ref } from 'vue'
import { useToast } from 'wot-design-uni'

const toast = useToast()

const isLoading = ref(true)
const practiceDetail = ref<PracticeResult | null>(null)
const practiceId = ref<number | null>(null)

// 计算属性
const totalQuestions = computed(() => practiceDetail.value?.questions?.length || 0)

// 检查用户答案是否正确
const isUserAnswerCorrect = (question: any) => {
  if (!question || !question.answer || !question.userAnswer) return false

  if (question.type === 'MULTIPLE_CHOICE' || question.type === 'MULTIPLE') {
    // 多选题：比较排序后的答案
    const userAnswer = Array.isArray(question.userAnswer)
      ? question.userAnswer
      : question.userAnswer.split(',')
    const sortedUserAnswer = userAnswer.sort().join(',')
    const sortedCorrectAnswer = question.answer.split(',').sort().join(',')
    return sortedUserAnswer === sortedCorrectAnswer
  } else {
    // 单选题：直接比较
    return question.userAnswer === question.answer
  }
}

// 计算正确和错误题数
const correctQuestions = computed(() => {
  if (!practiceDetail.value?.questions) return 0

  let correct = 0
  practiceDetail.value.questions.forEach(question => {
    if (question.userAnswer && isUserAnswerCorrect(question)) {
      correct++
    }
  })
  return correct
})

const wrongQuestions = computed(() => {
  if (!practiceDetail.value?.questions) return 0

  let wrong = 0
  practiceDetail.value.questions.forEach(question => {
    if (question.userAnswer && !isUserAnswerCorrect(question)) {
      wrong++
    }
  })
  return wrong
})

// 获取练习详情
async function fetchPracticeDetail(id: number) {
  isLoading.value = true
  try {
    const data = await getPracticeDetail({ practiceId: id })
    practiceDetail.value = data
  } catch (error) {
    console.error('获取练习详情失败:', error)
    toast.error('获取练习详情失败')
  } finally {
    isLoading.value = false
  }
}

function onComplete() {
  // 返回练习列表
  uni.navigateBack({
    delta: 1,
  })
}

onLoad(options => {
  if (options?.practiceId) {
    practiceId.value = Number.parseInt(options.practiceId)
    fetchPracticeDetail(practiceId.value)
  } else {
    toast.error('缺少练习ID参数')
    setTimeout(() => {
      uni.navigateBack()
    }, 2000)
  }
})
</script>

<template>
  <view class="practice-complete-page">
    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 练习完成结果 -->
    <view v-else-if="practiceDetail" class="result-content">
      <view class="score-card">
        <view class="card-title score-card__title"> 练习完成 </view>
        <view class="score-card__content">
          <view class="success-icon-container">
            <view class="success-icon__outer-circle">
              <view class="success-icon__inner-circle">
                <view class="i-carbon-checkmark text-[24px] text-[#1677FF]" />
              </view>
            </view>
          </view>
          <text class="congrats-message"> 恭喜您完成了练习！ </text>
          <text class="practice-info">继续努力，提升自己的能力</text>
        </view>
      </view>

      <view class="stats-card">
        <view class="card-title stats-card__title"> 答题统计 </view>
        <view class="stats-card__content">
          <view class="stat-item">
            <view class="stat-item__circle stat-item__circle--correct">
              <text class="stat-item__value stat-item__value--correct">{{ correctQuestions }}</text>
            </view>
            <text class="stat-item__label">答对题数</text>
          </view>
          <view class="stat-item">
            <view class="stat-item__circle stat-item__circle--wrong">
              <text class="stat-item__value stat-item__value--wrong">{{ wrongQuestions }}</text>
            </view>
            <text class="stat-item__label">答错题数</text>
          </view>
          <view class="stat-item">
            <view class="stat-item__circle stat-item__circle--total">
              <text class="stat-item__value stat-item__value--total">{{ totalQuestions }}</text>
            </view>
            <text class="stat-item__label">总题数</text>
          </view>
        </view>
      </view>

      <view class="bottom-actions">
        <!-- <view
          class="action-button action-button--review"
          @click="onViewWrongQuestions"
        >
          查看错题
        </view> -->
        <view class="action-button action-button--complete" @click="onComplete"> 完成 </view>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else class="error-container">
      <text>无法加载练习结果，请返回重试。</text>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "练习完成",
    "navigationStyle": "default"
  }
}
</route>

<style scoped lang="scss">
.practice-complete-page {
  box-sizing: border-box;
  min-height: 100vh;
  padding-top: 1px;
  padding-bottom: calc(95px + env(safe-area-inset-bottom));
  background-color: #f7f7f5;
}

.loading-container,
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: #666;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.result-content {
  display: flex;
  flex-direction: column;
}

.card-title {
  font-weight: 500;
}

.score-card {
  margin: 16px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 8px;

  &__title {
    padding: 11px 16px;
    font-size: 16px;
    color: #1677ff;
    background-color: #f0f7ff;
  }

  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
  }
}

.success-icon-container {
  margin-bottom: 15px;
}

.success-icon__outer-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background-color: #e6f7ff;
  border-radius: 50%;
}

.success-icon__inner-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background-color: #bae7ff;
  border-radius: 50%;
}

.congrats-message {
  margin-bottom: 8px;
  font-family: 'PingFang SC', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1677ff;
}

.practice-info {
  font-family: 'PingFang SC', sans-serif;
  font-size: 13px;
  color: #999;
}

.stats-card {
  margin: 16px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 6px;

  &__title {
    padding: 11px 16px;
    font-size: 16px;
    color: #1677ff;
    background-color: #f0f7ff;
  }

  &__content {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 20px;
  }
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;

  &__circle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 52px;
    height: 52px;
    margin-bottom: 10px;
    border-radius: 50%;

    &--correct {
      background-color: #f6ffed;
    }

    &--wrong {
      background-color: #fff2f0;
    }

    &--total {
      background-color: #f0f7ff;
    }
  }

  &__value {
    font-family: 'PingFang SC', sans-serif;
    font-size: 18px;

    &--correct {
      color: #52c41a;
    }

    &--wrong {
      color: #f5222d;
    }

    &--total {
      color: #666;
    }
  }

  &__label {
    font-family: 'PingFang SC', sans-serif;
    font-size: 13px;
    color: #666;
  }
}

.bottom-actions {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 16px;
  padding-top: 12px;
  background-color: #fff;
  border-top: 1px solid #f4f4f4;
}

.action-button {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  font-family: 'PingFang SC', sans-serif;
  font-size: 15px;
  border-radius: 7.5px;

  &--review {
    margin-bottom: 12px;
    color: #1677ff;
    border: 1px dashed #1677ff;
  }

  &--complete {
    color: #fff;
    background-color: #37acfe;
  }
}
</style>
