<script setup lang="ts">
import type { ExamAnswerDetail } from '@/types/api/Exam'
import { getAnswerDetail } from '@/api/exam'
import { onLoad } from '@dcloudio/uni-app'
import { computed, ref } from 'vue'
import { useToast } from 'wot-design-uni'

const toast = useToast()

const examDetail = ref<ExamAnswerDetail | null>(null)
const answerId = ref<number | null>(null)
const loading = ref(true)

// 错题列表（只显示用户答错的题目）
const wrongQuestions = computed(() => {
  if (!examDetail.value?.questions) return []
  return examDetail.value.questions.filter((question: any) => question.correct === false)
})

// 获取考试详情
const fetchExamDetail = async () => {
  if (!answerId.value) {
    toast.error('答题ID不存在')
    return
  }

  try {
    loading.value = true
    const result = await getAnswerDetail({ answerId: answerId.value })

    if (!result) {
      toast.error('考试详情为空')
      return
    }

    if (!result.questions || result.questions.length === 0) {
      toast.error('考试题目为空')
      return
    }

    examDetail.value = result
  } catch (error) {
    console.error('获取考试详情失败:', error)
    toast.error('获取考试详情失败')
  } finally {
    loading.value = false
  }
}

// 获取题目类型文本
const getQuestionTypeText = (type: string) => {
  switch (type) {
    case 'SINGLE':
      return '单选题'
    case 'MULTIPLE':
      return '多选题'
    default:
      return type
  }
}

// 检查用户答案是否正确
// const isUserAnswerCorrect = (question: any) => {
//   if (!question || !question.answer) return false

//   const userAnswer = question.userAnswer
//   if (!userAnswer) return false

//   if (question.type === 'MULTIPLE') {
//     if (!Array.isArray(userAnswer)) return false
//     const sortedUserAnswer = userAnswer.sort().join(',')
//     const sortedCorrectAnswer = question.answer.split(',').sort().join(',')
//     return sortedUserAnswer === sortedCorrectAnswer
//   } else {
//     return userAnswer === question.answer
//   }
// }

// 检查选项是否被用户选中
const isOptionSelectedByUser = (question: any, optionKey: string) => {
  if (!question || !question.userAnswer) return false

  if (question.type === 'MULTIPLE') {
    return Array.isArray(question.userAnswer) && question.userAnswer.includes(optionKey)
  } else {
    return question.userAnswer === optionKey
  }
}

// 检查选项是否是正确答案
const isCorrectOption = (question: any, optionKey: string) => {
  if (!question || !question.answer) return false

  if (question.type === 'MULTIPLE') {
    return question.answer.split(',').includes(optionKey)
  } else {
    return question.answer === optionKey
  }
}

// 返回上一页
const handleGoBack = () => {
  uni.navigateBack()
}

onLoad((options: any) => {
  if (options.answerId) {
    answerId.value = Number(options.answerId)

    // 设置页面标题为错题回顾
    uni.setNavigationBarTitle({
      title: '错题回顾',
    })

    fetchExamDetail()
  }
})
</script>

<template>
  <view class="exam-review-page">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 考试回顾内容 -->
    <view v-else-if="examDetail && wrongQuestions.length > 0" class="review-content">
      <!-- 题目卡片 -->
      <view v-for="(question, index) in wrongQuestions" :key="question.id" class="question-card">
        <view class="question-header">
          <text>问题 {{ index + 1 }}：{{ getQuestionTypeText(question.type) }}</text>
        </view>

        <view class="question-content">
          <text>{{ question.title }}</text>
        </view>

        <view class="options-list">
          <view
            v-for="option in question?.options || []"
            :key="option.id"
            class="option"
            :class="{
              'user-selected': isOptionSelectedByUser(question, option.optionKey),
              'correct-answer': isCorrectOption(question, option.optionKey),
              'wrong-answer':
                isOptionSelectedByUser(question, option.optionKey) &&
                !isCorrectOption(question, option.optionKey),
            }"
          >
            <view class="option-indicator">
              <text>{{ option.optionKey }}</text>
            </view>
            <text class="option-text">{{ option.content }}</text>
            <!-- 标记图标 -->
            <!-- <view class="option-marks">
              <text v-if="isCorrectOption(question, option.optionKey)" class="correct-mark">✓</text>
              <text v-if="isOptionSelectedByUser(question, option.optionKey) && !isCorrectOption(question, option.optionKey)" class="wrong-mark">✗</text>
            </view> -->
          </view>
        </view>

        <!-- 答案解析 -->
        <view class="answer-analysis">
          <!-- 答题结果反馈 -->
          <!-- <view
            class="feedback-badge"
            :class="{
              'feedback-correct': isUserAnswerCorrect(question),
              'feedback-wrong': !isUserAnswerCorrect(question)
            }"
          >
            <text class="feedback-icon">
              {{ isUserAnswerCorrect(question) ? '✓' : '✗' }}
            </text>
            <text class="feedback-text">
              {{ isUserAnswerCorrect(question) ? '回答正确' : '回答错误' }}
            </text>
          </view> -->

          <text class="analysis-title">答案解析</text>
          <text class="analysis-content">您的答案：{{ question.userAnswer }}</text>
          <text class="analysis-content">正确答案：{{ question.answer }}</text>
          <text v-if="question.analysis" class="analysis-detail">{{ question.analysis }}</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else-if="!loading" class="empty-container">
      <text class="empty-text">没有错题</text>
      <text class="empty-desc">恭喜您，所有题目都答对了！</text>
      <view class="empty-actions">
        <wd-button type="primary" :round="false" custom-class="back-btn" @click="handleGoBack"
          >返回</wd-button
        >
      </view>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "考试回顾",
    "navigationStyle": "default"
  }
}
</route>

<style scoped lang="scss">
// 设计令牌
$colors: (
  primary: #1677ff,
  primary-light: #f0f7ff,
  secondary: #37acfe,
  success: #19be6b,
  success-light: rgb(25 190 107 / 10%),
  error: #ed4014,
  error-light: rgb(237 64 20 / 10%),
  text-primary: #333,
  text-secondary: #666,
  text-tertiary: #999,
  bg-page: #f7f7f5,
  bg-card: #fff,
  bg-light: #f5f7fa,
  border-light: #e8e8e8,
  border-medium: #d9d9d9,
  border-primary: #d6e8ff,
  white: #fff,
);

@function color($name) {
  @return map-get($colors, $name);
}

// 基础布局
.exam-review-page {
  min-height: 100vh;
  padding-bottom: 20px;
  background-color: color(bg-page);
}

.review-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding-top: 16px;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: color(text-secondary);
}

.loading-text {
  font-size: 14px;
  color: color(text-secondary);
}

// 进度区域
.progress-section {
  padding: 12px 18px;
  background-color: color(bg-card);
}

.progress-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 13px;
  color: color(text-secondary);
}

.question-count {
  display: flex;
  gap: 4px;
  align-items: center;
}

.label {
  color: color(text-secondary);
}

.value {
  color: color(primary);
}

// 题目卡片
.question-card {
  margin: 0 16px;
  overflow: hidden;
  background-color: color(bg-card);
  border-radius: 8px;
}

.question-header {
  padding: 8px 16px;
  font-size: 15px;
  font-weight: 500;
  color: color(primary);
  background-color: color(primary-light);
}

.question-content {
  padding: 15px 16px;
  font-size: 15px;
  line-height: 1.5;
  color: color(text-primary);
}

.options-list {
  padding: 0 16px 20px;
}

.option {
  position: relative;
  display: flex;
  align-items: center;
  padding: 10px;
  margin-bottom: 10px;
  background-color: color(bg-card);
  border: 1px solid color(border-light);
  border-radius: 6px;

  &.user-selected {
    background-color: color(primary-light);
    border-color: color(primary);
  }

  &.correct-answer {
    background-color: color(success-light);
    border-color: color(success);
  }

  &.wrong-answer {
    background-color: color(error-light);
    border-color: color(error);
  }
}

.option-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  margin-right: 10px;
  font-size: 14px;
  color: color(text-secondary);
  border: 1px solid color(border-medium);
  border-radius: 50%;
}

.option-text {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  color: color(text-primary);
}

.option-marks {
  display: flex;
  gap: 5px;
  align-items: center;
}

.correct-mark {
  font-size: 16px;
  font-weight: bold;
  color: color(success);
}

.wrong-mark {
  font-size: 16px;
  font-weight: bold;
  color: color(error);
}

// 答案解析
.answer-analysis {
  position: relative;
  padding: 15px;
  margin: 0 16px 20px;
  background-color: color(primary-light);
  border-radius: 6px;
}

.feedback-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 10;
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px 12px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
}

.feedback-correct {
  background-color: color(success-light);
  border: 1px solid #b7eb8f;
}

.feedback-wrong {
  background-color: color(error-light);
  border: 1px solid #ffa39e;
}

.feedback-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  font-size: 14px;
  font-weight: bold;
  border-radius: 50%;
}

.feedback-correct .feedback-icon {
  color: color(white);
  background-color: color(success);
}

.feedback-wrong .feedback-icon {
  color: color(white);
  background-color: color(error);
}

.feedback-text {
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
}

.feedback-correct .feedback-text {
  color: color(success);
}

.feedback-wrong .feedback-text {
  color: color(error);
}

.analysis-title {
  display: block;
  margin-bottom: 10px;
  font-size: 15px;
  font-weight: 500;
  color: color(primary);
}

.analysis-content,
.analysis-detail {
  display: block;
  font-size: 14px;
  line-height: 1.5;
  color: color(text-primary);
}

.analysis-content {
  margin-bottom: 5px;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 20px;
  color: color(text-secondary);
}

.empty-text {
  margin-bottom: 8px;
  font-size: 16px;
  color: color(text-secondary);
}

.empty-desc {
  margin-bottom: 20px;
  font-size: 14px;
  color: color(text-tertiary);
  text-align: center;
}

.empty-actions {
  display: flex;
  justify-content: center;
}

:deep(.back-btn) {
  --wd-button-br: 6px !important;
}
</style>
