<script setup lang="ts">
import type { ExamAnswerDetail } from '@/types/api/Exam'
import { getAnswerDetail } from '@/api/exam'
import { onLoad } from '@dcloudio/uni-app'
import dayjs from 'dayjs'
import { computed, ref } from 'vue'
import { useToast } from 'wot-design-uni'

const toast = useToast()
const isLoading = ref(true)
const examDetail = ref<ExamAnswerDetail | null>(null)

// 从路由参数获取 answerId
const answerId = ref<number | null>(null)

// 计算属性
const score = computed(() => examDetail.value?.score || 0)
const passScore = computed(() => examDetail.value?.passScore || 60)
const totalQuestions = computed(() => examDetail.value?.questions?.length || 0)

// 计算正确和错误题数
const correctQuestions = computed(() => {
  if (!examDetail.value?.questions || !examDetail.value?.answers) return 0

  let correct = 0
  examDetail.value.questions.forEach(question => {
    const userAnswer = examDetail.value?.answers[question.id]
    if (userAnswer && question.answer) {
      // 处理多选题答案比较
      if (Array.isArray(userAnswer)) {
        const sortedUserAnswer = userAnswer.sort().join(',')
        const sortedCorrectAnswer = question.answer.split(',').sort().join(',')
        if (sortedUserAnswer === sortedCorrectAnswer) {
          correct++
        }
      } else {
        // 单选题答案比较
        if (userAnswer === question.answer) {
          correct++
        }
      }
    }
  })
  return correct
})

const wrongQuestions = computed(() => totalQuestions.value - correctQuestions.value)

// 计算考试用时
const examTime = computed(() => {
  if (!examDetail.value?.startTime || !examDetail.value?.endTime) return '未知'

  const start = dayjs(examDetail.value.startTime)
  const end = dayjs(examDetail.value.endTime)
  const duration = end.diff(start, 'second')

  const hours = Math.floor(duration / 3600)
  const minutes = Math.floor((duration % 3600) / 60)
  const seconds = duration % 60

  if (hours > 0) {
    return `${hours}小时${minutes}分${seconds}秒`
  } else if (minutes > 0) {
    return `${minutes}分${seconds}秒`
  } else {
    return `${seconds}秒`
  }
})

// 计算平均每题用时
const avgTimePerQuestion = computed(() => {
  if (!examDetail.value?.startTime || !examDetail.value?.endTime || totalQuestions.value === 0)
    return '未知'

  const start = dayjs(examDetail.value.startTime)
  const end = dayjs(examDetail.value.endTime)
  const totalSeconds = end.diff(start, 'second')
  const avgSeconds = Math.round(totalSeconds / totalQuestions.value)

  if (avgSeconds >= 60) {
    const minutes = Math.floor(avgSeconds / 60)
    const seconds = avgSeconds % 60
    return `${minutes}分${seconds}秒`
  } else {
    return `${avgSeconds}秒`
  }
})

// 判断是否通过考试
const isPassed = computed(() => score.value >= passScore.value)

// 获取考试详情
async function fetchExamDetail(id: number) {
  isLoading.value = true
  try {
    const data = await getAnswerDetail({ answerId: id })
    examDetail.value = data

    // 动态设置页面标题
    if (data.examName) {
      uni.setNavigationBarTitle({
        title: data.examName,
      })
    }
  } catch {
    toast.error('获取考试结果失败')
  } finally {
    isLoading.value = false
  }
}

function onViewWrongQuestions() {
  if (wrongQuestions.value === 0) {
    toast.success('恭喜！您没有错题')
    return
  }

  // 跳转到错题页面
  uni.navigateTo({
    url: `/pages/training-center/exam/review?answerId=${answerId.value}`,
  })
}

onLoad(options => {
  if (options?.answerId) {
    answerId.value = Number.parseInt(options.answerId)
    fetchExamDetail(answerId.value)
  } else {
    toast.error('缺少考试ID参数')
    setTimeout(() => {
      uni.navigateBack()
    }, 2000)
  }
})
</script>

<template>
  <view class="exam-result-page">
    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <wd-loading size="24px" />
    </view>

    <!-- 考试结果 -->
    <view v-else-if="examDetail" class="result-content">
      <view class="score-card">
        <view class="card-title score-card__title"> 考试结果 </view>
        <view class="score-card__content">
          <view class="success-icon-container">
            <view class="success-icon__outer-circle" :class="{ failed: !isPassed }">
              <view class="success-icon__inner-circle" :class="{ failed: !isPassed }">
                <view v-if="isPassed" class="success-icon__checkmark" />
                <view v-else class="success-icon__close" />
              </view>
            </view>
          </view>
          <view class="score-display">
            <text class="score-value" :class="{ failed: !isPassed }">{{ score }}</text>
            <text class="score-unit">分</text>
          </view>
          <text class="congrats-message" :class="{ failed: !isPassed }">
            {{ isPassed ? '恭喜您通过了考试！' : '很遗憾，您未通过考试' }}
          </text>
          <text class="pass-score-info">及格分数：{{ passScore }}分</text>
        </view>
      </view>

      <view class="stats-card">
        <view class="card-title stats-card__title"> 答题统计 </view>
        <view class="stats-card__content">
          <view class="stat-item">
            <view class="stat-item__circle stat-item__circle--correct">
              <text class="stat-item__value stat-item__value--correct">{{ correctQuestions }}</text>
            </view>
            <text class="stat-item__label">正确题数</text>
          </view>
          <view class="stat-item">
            <view class="stat-item__circle stat-item__circle--wrong">
              <text class="stat-item__value stat-item__value--wrong">{{ wrongQuestions }}</text>
            </view>
            <text class="stat-item__label">错误题数</text>
          </view>
          <view class="stat-item">
            <view class="stat-item__circle stat-item__circle--total">
              <text class="stat-item__value stat-item__value--total">{{ totalQuestions }}</text>
            </view>
            <text class="stat-item__label">总题数</text>
          </view>
        </view>
      </view>

      <view class="duration-card">
        <view class="duration-card__row">
          <text class="duration-card__label">考试用时</text>
          <text class="duration-card__value">{{ examTime }}</text>
        </view>
        <view class="duration-card__row">
          <text class="duration-card__label-avg">平均每题用时：</text>
          <text class="duration-card__value-avg">{{ avgTimePerQuestion }}</text>
        </view>
      </view>

      <view class="bottom-actions">
        <wd-button
          type="primary"
          :round="false"
          custom-class="action-button"
          @click="onViewWrongQuestions"
        >
          查看错题 ({{ wrongQuestions }})
        </wd-button>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else class="error-container">
      <text>无法加载考试结果，请返回重试。</text>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "考试结果",
    "navigationStyle": "default"
  }
}
</route>

<style scoped lang="scss">
.exam-result-page {
  box-sizing: border-box;
  min-height: 100vh;
  padding-top: 1px;
  padding-bottom: calc(95px + env(safe-area-inset-bottom));
  background-color: #f7f7f5;
}

.loading-container,
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: #666;
}

.result-content {
  display: flex;
  flex-direction: column;
}

.card-title {
  font-weight: 500;
}

.score-card {
  margin: 16px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 8px;

  &__title {
    padding: 11px 16px;
    font-size: 16px;
    color: #1677ff;
    background-color: #f0f7ff;
  }

  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
  }
}

.success-icon-container {
  margin-bottom: 15px;
}

.success-icon__outer-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background-color: #e6f7ff;
  border-radius: 50%;

  &.failed {
    background-color: #fff1f0;
  }
}

.success-icon__inner-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background-color: #bae7ff;
  border-radius: 50%;

  &.failed {
    background-color: #ffccc7;
  }
}

.success-icon__checkmark {
  width: 24px;
  height: 24px;
  background-color: #1677ff;
  mask: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3e%3cpath fill='currentColor' d='M9 16.17L4.83 12l-1.42 1.41L9 19L21 7l-1.41-1.41z'/%3e%3c/svg%3e")
    no-repeat center;
  mask-size: contain;
}

.success-icon__close {
  width: 24px;
  height: 24px;
  background-color: #f5222d;
  mask: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3e%3cpath fill='currentColor' d='M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12z'/%3e%3c/svg%3e")
    no-repeat center;
  mask-size: contain;
}

.score-display {
  display: flex;
  align-items: flex-end;
  margin-bottom: 10px;
}

.score-value {
  font-family: 'PingFang SC', sans-serif;
  font-size: 24px;
  font-weight: 500;
  line-height: 1;
  color: #1677ff;

  &.failed {
    color: #f5222d;
  }
}

.score-unit {
  margin-left: 5px;
  font-family: 'PingFang SC', sans-serif;
  font-size: 15px;
  color: #666;
}

.congrats-message {
  margin-bottom: 10px;
  font-family: 'PingFang SC', sans-serif;
  font-size: 15px;
  color: #666;

  &.failed {
    color: #f5222d;
  }
}

.pass-score-info {
  font-family: 'PingFang SC', sans-serif;
  font-size: 13px;
  color: #999;
}

.stats-card {
  margin: 16px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 6px;

  &__title {
    padding: 11px 16px;
    font-size: 16px;
    color: #1677ff;
    background-color: #f0f7ff;
  }

  &__content {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 20px;
  }
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;

  &__circle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 52px;
    height: 52px;
    margin-bottom: 10px;
    border-radius: 50%;

    &--correct {
      background-color: #e6f7ff;
    }

    &--wrong {
      background-color: #fff1f0;
    }

    &--total {
      background-color: #f0f7ff;
    }
  }

  &__value {
    font-family: 'PingFang SC', sans-serif;
    font-size: 18px;

    &--correct {
      color: #1677ff;
    }

    &--wrong {
      color: #f5222d;
    }

    &--total {
      color: #666;
    }
  }

  &__label {
    font-family: 'PingFang SC', sans-serif;
    font-size: 13px;
    color: #666;
  }
}

.duration-card {
  padding: 16px;
  margin: 16px;
  background-color: #fff;
  border-radius: 8px;

  &__row {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &:not(:last-child) {
      margin-bottom: 10px;
    }
  }

  &__label {
    font-family: 'PingFang SC', sans-serif;
    font-size: 15px;
    color: #333;
  }

  &__value {
    font-family: 'PingFang SC', sans-serif;
    font-size: 15px;
    color: #1677ff;
  }

  &__label-avg {
    font-family: 'PingFang SC', sans-serif;
    font-size: 13px;
    color: #999;
  }

  &__value-avg {
    font-family: 'PingFang SC', sans-serif;
    font-size: 13px;
    color: #666;
  }
}

.bottom-actions {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 16px;
  padding-top: 12px;
  background-color: #fff;
  border-top: 1px solid #f4f4f4;
}

:deep(.action-button) {
  width: 100% !important;
  height: 44px !important;

  --wd-button-br: 8px !important;
}
</style>
