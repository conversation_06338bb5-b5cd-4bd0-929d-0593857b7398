<script setup lang="ts">
import type { ExamAnswerDetail, ExamQuestion, ExamQuestionOption } from '@/types/api/Exam'
import { submitAnswer as apiSubmitAnswer, getAnswerDetail, updateAnswerProgress } from '@/api/exam'
import { onLoad } from '@dcloudio/uni-app'
import dayjs from 'dayjs'
import { computed, onUnmounted, ref } from 'vue'
import { useMessage, useToast } from 'wot-design-uni'

const answerId = ref<number | null>(null)
const examDetail = ref<ExamAnswerDetail | null>(null)
const currentQuestionIndex = ref(0)
const userAnswers = ref<Record<string, any>>({})
const remainingTime = ref('')
const isLoading = ref(true)
const isSubmitted = ref(false)
const showQuestionListModal = ref(false)
const toast = useToast()
const message = useMessage()
let timer: any = null

onLoad(async (options) => {
  if (options?.answerId) {
    const id = Number.parseInt(options.answerId, 10)
    answerId.value = id
    await fetchExamDetail(id)
  }
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})

async function fetchExamDetail(id: number) {
  isLoading.value = true
  try {
    const data = await getAnswerDetail({ answerId: id })
    examDetail.value = data
    if (data.answers) {
      userAnswers.value = data.answers
    }

    // 动态设置页面标题为考试名称
    if (data.examName) {
      uni.setNavigationBarTitle({
        title: data.examName,
      })
    }

    setupTimer(data.startTime, data.duration)
  }
  catch {
    toast.error('获取考试详情失败')
  }
  finally {
    isLoading.value = false
  }
}

function setupTimer(startTimeStr: string, durationMinutes: number) {
  if (timer) {
    clearInterval(timer)
  }
  const startTime = dayjs(startTimeStr)
  const endTime = startTime.add(durationMinutes, 'minute')

  timer = setInterval(async () => {
    const now = dayjs()
    if (now.isAfter(endTime)) {
      if (timer)
        clearInterval(timer)
      remainingTime.value = '00:00:00'
      // 注释自动提交功能
      await submitAnswer(true)
      return
    }
    const diff = endTime.diff(now)
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((diff % (1000 * 60)) / 1000)
    remainingTime.value = [hours, minutes, seconds]
      .map(v => v.toString().padStart(2, '0'))
      .join(':')
  }, 1000)
}

const currentQuestion = computed<ExamQuestion | null>(() => {
  return examDetail.value?.questions[currentQuestionIndex.value] ?? null
})

const totalQuestions = computed(() => examDetail.value?.questions.length ?? 0)

const progress = computed(() => {
  if (totalQuestions.value === 0)
    return 0
  return ((currentQuestionIndex.value + 1) / totalQuestions.value) * 100
})

const questionTypeMap: Record<string, string> = {
  SINGLE: '单选题',
  MULTIPLE: '多选题',
}

const questionTypeText = computed(() => {
  return currentQuestion.value?.type ? questionTypeMap[currentQuestion.value.type] : ''
})

function selectOption(option: ExamQuestionOption) {
  if (isSubmitted.value || !currentQuestion.value?.id || !option.optionKey)
    return

  const questionId = currentQuestion.value.id
  const currentAnswer = userAnswers.value[questionId]

  if (currentQuestion.value.type === 'MULTIPLE') {
    const answers = Array.isArray(currentAnswer) ? [...currentAnswer] : []
    const index = answers.indexOf(option.optionKey)
    if (index > -1) {
      answers.splice(index, 1)
    }
    else {
      answers.push(option.optionKey)
    }
    userAnswers.value[questionId] = answers.sort()
  }
  else {
    userAnswers.value[questionId] = option.optionKey
  }
}

function isOptionSelected(optionKey: string): boolean {
  if (!currentQuestion.value?.id)
    return false
  const answer = userAnswers.value[currentQuestion.value.id]
  if (Array.isArray(answer)) {
    return answer.includes(optionKey)
  }
  return answer === optionKey
}

// 格式化答案数据，将多选题数组转换为逗号分隔的字符串
function formatAnswerData(answers: Record<string, any>): Record<string, string> {
  const formattedAnswers: Record<string, string> = {}

  for (const [questionId, answer] of Object.entries(answers)) {
    if (Array.isArray(answer)) {
      // 多选题：将数组转换为逗号分隔的字符串
      formattedAnswers[questionId] = answer.join(',')
    }
    else if (answer !== undefined && answer !== null) {
      // 单选题：直接转换为字符串
      formattedAnswers[questionId] = String(answer)
    }
  }

  return formattedAnswers
}

// 计算已答题目数量
const answeredCount = computed(() => {
  if (!examDetail.value?.questions)
    return 0
  return examDetail.value.questions.filter((_, index) => isQuestionAnswered(index)).length
})

// 显示提交确认对话框
async function showSubmitConfirm() {
  if (answeredCount.value !== totalQuestions.value) {
    toast.warning('请完成所有题目后再提交')
    return
  }

  try {
    await message.confirm({
      title: '提交确认',
      msg: '确定要提交答卷吗？提交后将无法修改答案',
      confirmButtonText: '确定提交',
      cancelButtonText: '取消',
    })
    // 用户确认提交
    await submitAnswer(false)
  }
  catch {
    // 用户取消提交，不做任何处理
  }
}

const answering = ref<boolean>(false)

async function submitAnswer(isAutoSubmit = false) {
  if (!answerId.value)
    return
  if (answering.value === true) {
    return
  }
  try {
    answering.value = true
    const formattedAnswers = formatAnswerData(userAnswers.value)
    await apiSubmitAnswer({ id: answerId.value, answerData: formattedAnswers })
    isSubmitted.value = true
    if (!isAutoSubmit) {
      toast.success('提交成功')
      // 提交成功后跳转到成绩页面
      setTimeout(() => {
        answering.value = false
        uni.redirectTo({
          url: `/pages/training-center/exam/score?answerId=${answerId.value}`,
        })
      }, 1500) // 1.5秒后跳转，让用户看到成功提示
    }
    else {
      toast.show('时间到，已自动交卷')
      answering.value = false
      // 自动提交后也跳转到成绩页面
      setTimeout(() => {
        uni.redirectTo({
          url: `/pages/training-center/exam/score?answerId=${answerId.value}`,
        })
      }, 2000)
    }
  }
  catch {
    toast.error('提交失败')
    answering.value = false
  }
}

async function nextQuestion() {
  if (!answerId.value)
    return

  if (currentQuestionIndex.value < totalQuestions.value - 1) {
    const formattedAnswers = formatAnswerData(userAnswers.value)
    updateAnswerProgress({ id: answerId.value, answerData: formattedAnswers })
    currentQuestionIndex.value++
    isSubmitted.value = false
  }
  else {
    // 最后一题，显示提交确认弹窗
    showSubmitConfirm()
  }
}

async function prevQuestion() {
  if (!answerId.value)
    return
  const formattedAnswers = formatAnswerData(userAnswers.value)
  updateAnswerProgress({ id: answerId.value, answerData: formattedAnswers })
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--
    isSubmitted.value = false
  }
}

function showQuestionList() {
  showQuestionListModal.value = true
}

function closeQuestionList() {
  showQuestionListModal.value = false
}

function jumpToQuestion(index: number) {
  currentQuestionIndex.value = index
  closeQuestionList()
}

// 判断题目是否已答
function isQuestionAnswered(questionIndex: number): boolean {
  const question = examDetail.value?.questions[questionIndex]
  if (!question?.id)
    return false
  const answer = userAnswers.value[question.id]
  return answer !== undefined && answer !== null && answer !== ''
}
</script>

<template>
  <view class="exam-page">
    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <wd-loading size="24px" />
    </view>

    <!-- 考试内容 -->
    <view v-else-if="examDetail && currentQuestion" class="exam-content">
      <!-- 进度信息 -->
      <view class="progress-section">
        <view class="progress-info">
          <view class="time-info">
            <view class="clock-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="10"
                height="10"
                viewBox="0 0 10 10"
                fill="none"
              >
                <path
                  d="M5.25391 0.83252L5.25391 0.421399C5.25391 0.207538 5.08008 0.0337158 4.86622 0.0337158C4.65235 0.0337158 4.47853 0.207538 4.47853 0.421399V0.851075C4.19922 0.881347 3.92578 0.937011 3.65039 1.02099C1.22852 1.75244 -0.146484 4.3178 -0.0751949 6.7396C-0.0126949 6.94467 0.203125 7.0599 0.408203 6.99838C0.613281 6.93685 0.729492 6.72006 0.666992 6.51499C0.0585941 4.50234 1.20117 2.37057 3.21387 1.76316C5.22656 1.15576 7.35832 2.29733 7.96573 4.30998C8.57413 6.32262 7.43156 8.45439 5.41891 9.06179C3.96875 9.50026 2.39653 9.03836 1.41414 7.88507C1.27547 7.72199 1.03035 7.70246 0.867267 7.84112C0.704181 7.97978 0.68465 8.22489 0.823312 8.38798C1.71099 9.42702 2.99414 9.99999 4.31734 9.99999C4.75977 9.99999 5.20703 9.93554 5.64355 9.80371C8.06535 9.07131 9.43938 6.50595 8.70793 4.08512C8.1386 2.19845 6.45598 0.947509 4.59473 0.832275V0.83252H5.25391Z"
                  fill="#1677FF"
                />
                <path
                  d="M5.66097 2.1594C5.10337 1.27662 4.32312 0.546167 3.40615 0.047158C3.21768 -0.0553782 2.98234 0.0149323 2.88078 0.203404C2.77922 0.391875 2.84855 0.62722 3.03605 0.728779C3.83778 1.16431 4.51843 1.80297 5.00572 2.57443C5.07993 2.69161 5.20493 2.75509 5.33383 2.75509C5.40512 2.75509 5.47641 2.73556 5.54086 2.69552C5.72152 2.58029 5.7762 2.34104 5.66097 2.1594ZM0.387684 3.29023C0.173823 3.29023 0 3.46405 0 3.67791V5.39662C0.00292969 5.5011 0.04785 5.59485 0.117184 5.66223L0.889623 6.54209C0.965792 6.629 1.07321 6.67392 1.18063 6.67392C1.27145 6.67392 1.36227 6.6417 1.43648 6.57725C1.59761 6.43565 1.61324 6.19151 1.47164 6.03039L0.774392 5.23646V3.67791C0.775368 3.46405 0.601545 3.29023 0.387684 3.29023Z"
                  fill="#1677FF"
                />
              </svg>
            </view>
            <text class="label">剩余时间：</text>
            <text class="value">{{ remainingTime }}</text>
          </view>
          <view class="question-count">
            <text class="label">题目：</text>
            <text class="value">{{ currentQuestionIndex + 1 }}/{{ totalQuestions }}</text>
          </view>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: `${progress}%` }" />
        </view>
      </view>

      <!-- 题目卡片 -->
      <view class="question-card">
        <view class="question-header">
          <text>问题 {{ currentQuestionIndex + 1 }}：{{ questionTypeText }}</text>
        </view>

        <view class="question-content">
          <text>{{ currentQuestion.title }}</text>
        </view>

        <view class="options-list">
          <view
            v-for="option in currentQuestion?.options || []"
            :key="option.id"
            class="option"
            :class="{ selected: isOptionSelected(option.optionKey!) }"
            @click="selectOption(option)"
          >
            <view
              class="option-indicator"
              :class="{ selected: isOptionSelected(option.optionKey!) }"
            >
              <text>{{ option.optionKey }}</text>
            </view>
            <text class="option-text">{{ option.content }}</text>
          </view>
        </view>

        <!-- 答案解析 -->
        <!-- <view v-if="isSubmitted" class="answer-analysis">
          <text class="analysis-title">答案解析</text>
          <text class="analysis-content">正确答案：{{ currentQuestion.answer }} - {{ getCorrectOptionText(currentQuestion) }}</text>
          <text class="analysis-detail">{{ currentQuestion.analysis }}</text>
        </view> -->
      </view>

      <!-- 底部导航 -->
      <view class="navigation-bar">
        <wd-button
          type="primary"
          :round="false"
          icon="arrow-left"
          custom-class="nav-button"
          @click="prevQuestion"
        >
          上一题
        </wd-button>
        <wd-button plain :round="false" custom-class="nav-button" @click="showQuestionList">
          题目列表
        </wd-button>
        <wd-button
          type="primary"
          :round="false"
          custom-class="nav-button"
          use-default-slot
          :loading="answering"
          @click="nextQuestion"
        >
          <view class="btn-next-content">
            <text>{{ currentQuestionIndex === totalQuestions - 1 ? '交卷' : '下一题' }}</text>
            <wd-icon
              v-if="currentQuestionIndex < totalQuestions - 1"
              name="arrow-right"
              custom-class="right-icon"
            />
          </view>
        </wd-button>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else class="error-container">
      <text>无法加载考试内容，请返回重试。</text>
    </view>

    <!-- 题目列表弹窗 -->
    <view v-if="showQuestionListModal" class="modal-overlay" @click="closeQuestionList">
      <view class="modal" @click.stop>
        <view class="modal__header">
          <text class="modal__title">题目列表</text>
          <view class="modal__close" @click="closeQuestionList">
            <text>×</text>
          </view>
        </view>

        <view class="modal__body">
          <!-- 状态说明 -->
          <view class="status-legend">
            <text class="legend__title">状态说明</text>
            <view class="legend__items">
              <view class="legend__item">
                <view class="legend__indicator legend__indicator--answered" />
                <text class="legend__text">已答题</text>
              </view>
              <view class="legend__item">
                <view class="legend__indicator legend__indicator--current" />
                <text class="legend__text">当前题</text>
              </view>
            </view>
          </view>

          <!-- 全部题目 -->
          <view class="questions-section">
            <text class="questions-section__title">全部题目</text>
            <view class="questions-grid">
              <view
                v-for="(question, index) in examDetail?.questions || []"
                :key="question.id"
                class="question-grid-item"
                :class="{
                  'question-grid-item--answered': isQuestionAnswered(index),
                  'question-grid-item--current': index === currentQuestionIndex,
                }"
                @click="jumpToQuestion(index)"
              >
                <text>{{ index + 1 }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "考试"
  }
}
</route>

<style scoped lang="scss">
// 设计令牌
$colors: (
  primary: #1677ff,
  primary-light: #f0f7ff,
  secondary: #37acfe,
  success: #52c41a,
  success-light: #f6ffed,
  text-primary: #333,
  text-secondary: #666,
  text-tertiary: #999,
  bg-page: #f7f7f5,
  bg-card: #fff,
  bg-light: #f5f7fa,
  border-light: #e8e8e8,
  border-medium: #d9d9d9,
  border-primary: #d6e8ff,
  white: #fff,
);

@function color($name) {
  @return map-get($colors, $name);
}

// 基础布局
.exam-page {
  box-sizing: border-box;
  min-height: 100vh;
  padding-bottom: 95px;
  background-color: color(bg-page);
}

.exam-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.loading-container,
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: color(text-secondary);
}

// 进度区域
.progress-section {
  padding: 12px 18px;
  background-color: color(bg-card);
}

.progress-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 13px;
  color: color(text-secondary);
}

.time-info,
.question-count {
  display: flex;
  gap: 4px;
  align-items: center;
}

.clock-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;

  svg {
    width: 10px;
    height: 10px;
  }
}

.label {
  color: color(text-secondary);
}

.value {
  color: color(primary);
}

.progress-bar {
  height: 2px;
  overflow: hidden;
  background-color: color(primary-light);
  border-radius: 1px;
}

.progress-fill {
  height: 100%;
  background-color: color(primary);
  border-radius: 1px;
  transition: width 0.3s ease;
}

// 题目卡片
.question-card {
  margin: 0 16px;
  overflow: hidden;
  background-color: color(bg-card);
  border-radius: 8px;
}

.question-header {
  padding: 8px 16px;
  font-size: 15px;
  font-weight: 500;
  color: color(primary);
  background-color: color(primary-light);
}

.question-content {
  padding: 15px 16px;
  font-size: 15px;
  line-height: 1.5;
  color: color(text-primary);
}

.options-list {
  padding: 0 16px 20px;
}

.option {
  display: flex;
  align-items: center;
  padding: 10px;
  margin-bottom: 10px;
  cursor: pointer;
  background-color: color(bg-card);
  border: 1px solid color(border-light);
  border-radius: 6px;
  transition: all 0.2s ease;

  &.selected {
    background-color: color(primary-light);
    border-color: color(primary);
  }

  &:hover {
    border-color: color(primary);
  }
}

.option-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  margin-right: 10px;
  font-size: 14px;
  color: color(text-secondary);
  border: 1px solid color(border-medium);
  border-radius: 50%;
  transition: all 0.2s ease;

  &.selected {
    color: color(primary);
    background-color: color(bg-card);
    border-color: color(primary);
  }
}

.option-text {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  color: color(text-primary);
}

// 答案解析
.answer-analysis {
  padding: 15px;
  margin: 0 16px 20px;
  background-color: color(primary-light);
  border-radius: 6px;
}

.analysis-title {
  display: block;
  margin-bottom: 10px;
  font-size: 15px;
  font-weight: 500;
  color: color(primary);
}

.analysis-content,
.analysis-detail {
  display: block;
  font-size: 14px;
  line-height: 1.5;
  color: color(text-primary);
}

.analysis-content {
  margin-bottom: 5px;
}

// 导航栏
.navigation-bar {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: space-around;
  padding: 12px 16px;
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
  background-color: color(bg-card);
  border-top: 1px solid #f4f4f4;

  :deep(.nav-button) {
    flex: 1;
    margin: 0;
  }
}

.btn-next-content {
  display: flex;
  align-items: center;
  justify-content: center;

  .right-icon {
    margin-left: 4px;
    font-size: 14px;
    color: color(white);
  }
}

// 弹窗样式
.modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(0 0 0 / 50%);
}

.modal {
  width: 90%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
  background-color: color(bg-card);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgb(0 0 0 / 10%);

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid color(border-light);
  }

  &__title {
    font-size: 18px;
    font-weight: 600;
    color: color(text-primary);
  }

  &__close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    font-size: 20px;
    color: color(text-secondary);
    cursor: pointer;
    transition: color 0.2s ease;

    &:hover {
      color: color(text-primary);
    }
  }

  &__body {
    max-height: calc(80vh - 80px);
    padding: 20px;
    overflow-y: auto;
  }
}

.status-legend {
  margin-bottom: 24px;
}

.legend {
  &__title {
    display: block;
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
    color: color(text-primary);
  }

  &__items {
    display: flex;
    gap: 20px;
  }

  &__item {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  &__indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;

    &--answered {
      background-color: color(success);
      border: 2px solid color(success);
    }

    &--current {
      background-color: color(primary);
      border: 2px solid color(primary);
    }
  }

  &__text {
    font-size: 14px;
    color: color(text-secondary);
  }
}

.questions-section {
  &__title {
    display: block;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: color(text-primary);
  }
}

.questions-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
}

.question-grid-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  color: color(text-secondary);
  cursor: pointer;
  background-color: color(bg-card);
  border: 2px solid color(border-medium);
  border-radius: 8px;
  transition: all 0.2s ease;

  &--answered {
    color: color(success);
    background-color: color(success-light);
    border-color: color(success);
  }

  &--current {
    color: color(primary);
    background-color: color(primary-light);
    border-color: color(primary);
  }

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: scale(1.05);
  }
}
</style>
