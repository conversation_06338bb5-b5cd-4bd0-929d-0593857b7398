<script setup lang="ts">
import type { Station } from '@/types/api/Station'
import { getStationByStationCode, updateStationLocation } from '@/api/station'
import { useChannel } from '@/composables/useChannel'
import { onLoad } from '@dcloudio/uni-app'
import { getLocation, navigateBack, navigateTo } from '@uni-helper/uni-promises'
import { computed, ref } from 'vue'
import { useMessage, useToast } from 'wot-design-uni'

const toast = useToast()
const message = useMessage()
const { eventEmit } = useChannel()

const form = ref<Partial<Station>>({
  stationCode: '',
  name: '',
  phone: '',
  address: '',
  opName: '',
  longitude: undefined,
  latitude: undefined,
  moduleQuantity: undefined,
  status: '',
  createdAt: '',
})

const stationCodeQuery = ref<string | null>(null)
const isLoading = ref(false)
const showActionSheet = ref(false)
const actionSheetActions = ref([
  { name: '自动更新', value: 'auto' },
  { name: '地图选择', value: 'map' },
])

const location = computed(() => {
  if (!form.value.longitude || !form.value.latitude) {
    return ''
  }
  return `${form.value.longitude},${form.value.latitude}`
})

onLoad(async options => {
  if (options?.stationCode) {
    stationCodeQuery.value = options.stationCode
    await fetchStationDetails(options.stationCode)
  }
})

async function fetchStationDetails(code: string) {
  isLoading.value = true
  try {
    const data = await getStationByStationCode({ stationCode: code })
    form.value = data
  } catch (error) {
    console.error('Failed to fetch station details:', error)
    toast.error('获取电站详情失败')
  } finally {
    isLoading.value = false
  }
}

async function handleSubmit() {
  if (!form.value.stationCode || !form.value.longitude || !form.value.latitude) {
    toast.error('电站信息不完整，无法更新')
    return
  }
  try {
    await message.confirm({
      title: '更新电站经纬度',
      msg: `确认更新电站经纬度吗？`,
      confirmButtonText: '确认',
      cancelButtonText: '取消',
    })
    isLoading.value = true
    await updateStationLocation({
      stationCode: form.value.stationCode!,
      newLongitude: form.value.longitude!,
      newLatitude: form.value.latitude!,
    })
    toast.success('更新成功')
    eventEmit('updateLocation', {
      longitude: form.value.longitude,
      latitude: form.value.latitude,
    })
    navigateBack()
  } catch (error: any) {
    if (error.action === 'cancel') {
      // 用户取消操作，不提示
    } else {
      console.error('Failed to update station location:', error)
      toast.error('更新失败')
    }
  } finally {
    isLoading.value = false
  }
}

async function updateLocation() {
  showActionSheet.value = true
}

async function handleActionSelect(action: { item: { value: string } }) {
  showActionSheet.value = false
  if (action.item.value === 'auto') {
    try {
      const res = await getLocation({ type: 'gcj02' })
      form.value.longitude = res.longitude
      form.value.latitude = res.latitude
      toast.success('位置已自动更新')
    } catch (error) {
      console.error('Failed to get location:', error)
      toast.error('自动获取位置失败')
    }
  } else if (action.item.value === 'map') {
    const currentLat = form.value.latitude
    const currentLng = form.value.longitude
    let navUrl = '/pages/common/map-picker'
    if (currentLat && currentLng) {
      navUrl += `?latitude=${currentLat}&longitude=${currentLng}`
    }

    navigateTo({
      url: navUrl,
      events: {
        acceptDataFromMapPicker(data: { latitude: number; longitude: number }) {
          form.value.latitude = data.latitude
          form.value.longitude = data.longitude
          toast.success('位置已通过地图更新')
        },
      },
      fail: err => {
        console.error('Failed to navigate to map picker:', err)
        toast.error('无法打开地图选择页面')
      },
    })
  }
}

function handleActionClose() {
  showActionSheet.value = false
}
</script>

<template>
  <view class="page-container">
    <wd-form :model="form" label-width="100px" label-align="right">
      <wd-cell-group border>
        <wd-input label-width="100px" :model-value="form.stationCode" label="电站编码" readonly />
        <wd-input label-width="100px" :model-value="form.name" label="电站名称" readonly />
        <wd-input label-width="100px" :model-value="form.phone" label="联系方式" readonly />
        <wd-input
          label-width="100px"
          :model-value="form.address"
          label="电站地址"
          :textarea="true"
          readonly
        />
        <wd-input label-width="100px" :model-value="form.opName" label="所属代理商" readonly />
        <wd-input label-width="100px" :model-value="location" label="经纬度" readonly>
          <template #suffix>
            <text class="text-primary" @click="updateLocation">更新</text>
          </template>
        </wd-input>
        <wd-form-item prop="roofHighImage" label="电站照片">
          <wd-img
            :width="100"
            :height="100"
            :src="form.roofHighImage"
            mode="aspectFill"
            :enable-preview="true"
          />
        </wd-form-item>
        <wd-form-item prop="roofHighImage" label="电站图纸">
          <wd-img
            :width="100"
            :height="100"
            :src="form.roofSizeImage"
            mode="aspectFill"
            :enable-preview="true"
          />
        </wd-form-item>
      </wd-cell-group>
    </wd-form>

    <view class="form-actions">
      <wd-button type="primary" block :loading="isLoading" :round="false" @click="handleSubmit">
        保存
      </wd-button>
    </view>

    <wd-action-sheet
      v-model="showActionSheet"
      :actions="actionSheetActions"
      title="选择更新方式"
      cancel-text="取消"
      @select="handleActionSelect"
      @close="handleActionClose"
      @cancel="handleActionClose"
    />
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "编辑电站",
    "navigationStyle": "default"
  }
}
</route>

<style scoped lang="scss">
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f7f8fa;
}

.wd-form {
  flex-grow: 1;
  padding-bottom: 80px;
  overflow-y: auto;

  :deep(.wd-input__suffix) {
    z-index: 5;
  }

  :deep(.wd-cell__wrapper) {
    &::after {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      height: 1px;
      content: '';
      background: var(--wot-input-border-color, #dadada);
      transition: background-color 0.2s ease-in-out;
      transform: scaleY(0.5);
    }
  }
}

.form-actions {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  padding: 15px;
  background-color: #fff;
  border-top: 1px solid #ebedf0;
}

:deep(.wd-cell__value) {
  flex: 1;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
