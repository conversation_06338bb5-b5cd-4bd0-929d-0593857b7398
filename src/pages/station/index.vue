<script lang="ts" setup>
import type { Station, StationPageParams } from '@/types/api/Station'
import { getStationPage } from '@/api/station'
import StationCard from '@/pages/station/components/StationCard.vue'
import { navigateTo } from '@uni-helper/uni-promises'
import { reactive, ref } from 'vue'

const keyword = ref('')

const paging = ref<ZPagingRef>()
const stationList = ref<Station[]>([])
const pagnation = reactive({
  pageSize: 20,
  total: 0,
})

const onSearch = () => {
  paging.value?.reload()
}

const queryList = async (pageNum: number, pageSize: number) => {
  const params: StationPageParams = {
    pageNum,
    pageSize,
  }
  if (keyword.value) {
    if (/^\d{11}$/.test(keyword.value)) {
      params.phone = keyword.value
    } else if (/^\d*$/.test(keyword.value)) {
      params.stationCode = keyword.value
    } else {
      params.name = keyword.value
    }
  }

  try {
    const res = await getStationPage(params)
    paging.value?.completeByTotal(res.content, res.totalElements)
    pagnation.total = res.totalElements
  } catch {
    paging.value?.complete(false)
  }
}

const goToEdit = (item: Station) => {
  navigateTo({
    url: `/pages/station/edit?stationCode=${item.stationCode}`,
    events: {
      updateLocation(data: { latitude: number; longitude: number }) {
        const stationToUpdate = stationList.value.find(s => s.stationCode === item.stationCode)
        if (stationToUpdate) {
          stationToUpdate.longitude = data.longitude
          stationToUpdate.latitude = data.latitude
        }
      },
    },
  })
}
</script>

<template>
  <view class="station-page">
    <search-bar
      v-model="keyword"
      placeholder="请输入电站编号/电站名称/手机号码"
      @search="onSearch"
    />

    <view class="px-3 text-sm text-gray">
      <text>结果：</text>
      <text class="text-primary">{{ pagnation.total }}</text>
      <text>户</text>
    </view>

    <!-- 电站列表 -->
    <z-paging
      ref="paging"
      v-model="stationList"
      class="station-list-paging"
      :fixed="false"
      :default-page-size="pagnation.pageSize"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
      @query="queryList"
    >
      <view class="station-list-content">
        <station-card
          v-for="item in stationList"
          :key="`${item.stationCode}-${item.longitude}-${item.latitude}`"
          :item="item"
          @click="goToEdit"
        />
      </view>
    </z-paging>
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "电站中心"
  }
}
</route>

<style scoped lang="scss">
.station-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .station-list-paging {
    flex: 1;
  }

  .station-list-content {
    padding: 8px 12px;
  }

  .filter-popup-content {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
}
</style>
