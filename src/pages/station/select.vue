<script lang="ts" setup>
import type { Station, StationPageParams } from '@/types/api/Station'
import { getStationPage } from '@/api/station'
import { onLoad } from '@dcloudio/uni-app'
import { getCurrentInstance, ref } from 'vue'

const keyword = ref('')

const paging = ref<ZPagingRef>()
const stationList = ref<Station[]>([])
const pageSize = ref(20)

const onSearch = () => {
  paging.value?.reload()
}

const queryList = async (pageNum: number, pageSize: number) => {
  const params: StationPageParams = {
    pageNum,
    pageSize,
  }
  if (keyword.value) {
    if (/^\d{11}$/.test(keyword.value)) {
      params.phone = keyword.value
    } else if (/^\d*$/.test(keyword.value)) {
      params.stationCode = keyword.value
    } else {
      params.name = keyword.value
    }
  }

  try {
    const res = await getStationPage(params)
    paging.value?.completeByTotal(res.content, res.totalElements)
  } catch {
    paging.value?.complete(false)
  }
}

let pageEventChannel: UniApp.EventChannel | undefined

onLoad(() => {
  const instance = getCurrentInstance()
  if (instance?.proxy) {
    // 使用 as any 来绕过类型检查，因为 proxy 的类型 ComponentPublicInstance 不直接包含 getOpenerEventChannel
    const proxy = instance.proxy as any
    if (typeof proxy.getOpenerEventChannel === 'function') {
      pageEventChannel = proxy.getOpenerEventChannel()
    } else {
      console.warn('getOpenerEventChannel is not a function on the current page instance proxy.')
    }
  } else {
    console.warn('Could not get current instance proxy in onLoad.')
  }
})

const selectStation = (station: Station) => {
  if (pageEventChannel) {
    pageEventChannel.emit('selectStation', { stationCode: station.stationCode })
    uni.navigateBack()
  } else {
    console.warn(
      'pageEventChannel is not available. Navigating back without emitting selectStation event.',
    )
    uni.navigateBack()
  }
}
</script>

<template>
  <view class="monitor-page">
    <search-bar
      v-model="keyword"
      placeholder="请输入电站编号/户主姓名/手机号码"
      @search="onSearch"
    />

    <z-paging
      ref="paging"
      v-model="stationList"
      class="station-list-paging"
      :fixed="false"
      :default-page-size="pageSize"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
      use-virtual-list
      @query="queryList"
    >
      <view class="station-list-content">
        <view
          v-for="item in stationList"
          :key="item.id || item.stationCode"
          class="station-card"
          @click="selectStation(item)"
        >
          <view class="card-header">
            <text class="title">{{ item.stationCode }}</text>
          </view>
          <view class="card-meta">
            <text>{{ item.createdAt }}</text>
          </view>
          <view class="card-body">
            <view class="detail-item">
              <text class="label">电站名称</text>
              <text class="value">{{ item.name }}</text>
            </view>
            <view class="detail-item">
              <text class="label">电站地址</text>
              <text class="value">{{ item.address }}</text>
            </view>
            <view class="detail-item">
              <text class="label">联系方式</text>
              <text class="value">{{ item.phone }}</text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "选择电站"
  }
}
</route>

<style scoped lang="scss">
.monitor-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .station-list-paging {
    flex: 1;

    .station-list-paging__empty-message {
      padding: 16px;
      color: #6b7280;
      text-align: center;
    }
  }

  .station-list-content {
    padding: 8px 12px;
  }

  .station-card {
    padding: 12px;
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 5%);

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }
    }

    .card-meta {
      padding-bottom: 8px;
      margin-bottom: 10px;
      font-size: 12px;
      color: #999;
      border-bottom: 1px solid #f0f0f0;
    }

    .card-body {
      .detail-item {
        display: flex;
        margin-bottom: 4px;
        font-size: 14px;
        line-height: 1.6;

        .label {
          flex-shrink: 0;
          width: 70px;
          margin-right: 8px;
          color: #666;
        }

        .value {
          flex: 1;
          color: #333;
          word-break: break-all;
        }
      }
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

:deep(.uni-input-placeholder) {
  font-size: 12px;
  color: #9a9a9a;
}
</style>
