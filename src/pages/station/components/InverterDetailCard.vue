<script setup lang="ts">
import type {
  Inverter,
  InverterMpptData,
  StationInverterPowerData,
  StationInverterRealtimeData,
  StationWeatherRadiation,
} from '@/types/api/Station'
import type { EChartsOption } from 'echarts/types/dist/shared'
import {
  getInverterDataList,
  getInverterElecData,
  getInverterMpptDataList,
  getInverterWeatherRadiationData,
} from '@/api/station'
import EchartWrapper from '@/components/charts/EchartWrapper.vue'
import { baseChartOption } from '@/plugins/echarts'
import dayjs from 'dayjs'
import { computed, onMounted, ref } from 'vue'
import MpptEditor from './MpptEditor.vue'

interface Props {
  inverter: Inverter
}

const props = defineProps<Props>()

// MPPT信息相关
interface MpptInfo {
  name: string
  total: number
  pv: Array<{
    name: string
    total: number
  }>
}

const mpptInfoList = ref<MpptInfo[]>([])
const showMpptEditor = ref(false)

const chartData = ref<StationInverterRealtimeData[]>([])
const elecData = ref<StationInverterPowerData>({})
const mpptData = ref<InverterMpptData[]>([])
const radiationData = ref<StationWeatherRadiation[]>([])

const chartLoading = ref(true)

const { xAxis, yAxis, series, tooltip, ...restBaseOptions } = baseChartOption

// MPPT功率图表配置
const mpptChartOption = computed<EChartsOption>(() => {
  if (!mpptData.value || mpptData.value.length === 0) {
    return {
      ...restBaseOptions,
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          color: '#999',
          fontSize: 14,
        },
      },
    }
  }

  // 获取唯一的时间点
  const timeSet = new Set(mpptData.value.map(item => item.dataTimestamp))
  const xAxisData = Array.from(timeSet)
    .sort()
    .map(time => dayjs(time).format('HH:mm'))

  // 按MPPT名称分组数据
  const seriesData: Record<string, number[]> = {}
  const mpptNames = Array.from(new Set(mpptData.value.map(item => item.mpptName)))

  mpptNames.forEach(mpptName => {
    seriesData[mpptName] = []
    xAxisData.forEach((_, index) => {
      const timePoint = Array.from(timeSet).sort()[index]
      const dataPoint = mpptData.value.find(
        item => item.mpptName === mpptName && item.dataTimestamp === timePoint,
      )
      seriesData[mpptName].push(dataPoint ? dataPoint.power : 0)
    })
  })

  const series = mpptNames.map(mpptName => ({
    name: mpptName,
    type: 'line' as const,
    data: seriesData[mpptName],
    smooth: true,
  }))

  return {
    ...restBaseOptions,
    xAxis: {
      type: 'category' as const,
      data: xAxisData,
    },
    yAxis: {
      type: 'value' as const,
      name: '功率(W)',
    },
    series,
    legend: {
      show: true,
      bottom: 0,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
    },
  }
})

// 辐射度图表配置
const radiationChartOption = computed<EChartsOption>(() => {
  if (!radiationData.value || radiationData.value.length === 0) {
    return {
      ...restBaseOptions,
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          color: '#999',
          fontSize: 14,
        },
      },
    }
  }

  const xAxisData = radiationData.value.map(item => dayjs(item.timestamp).format('HH:mm'))
  const seriesData = radiationData.value.map(item => item.shortwaveRadiation || 0)

  return {
    ...restBaseOptions,
    xAxis: {
      type: 'category' as const,
      data: xAxisData,
    },
    yAxis: {
      type: 'value' as const,
      name: '辐射(W/m²)',
    },
    series: [
      {
        name: '辐射',
        type: 'line' as const,
        data: seriesData,
        smooth: true,
        itemStyle: {
          color: '#ff7f00',
        },
      },
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
    },
  }
})

// 发电功率与温度图表配置
const powerTempChartOption = computed<EChartsOption>(() => {
  if (!chartData.value || chartData.value.length === 0) {
    return {
      ...restBaseOptions,
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          color: '#999',
          fontSize: 14,
        },
      },
    }
  }

  const xAxisData = chartData.value.map(item => dayjs(item.dataTimestamp).format('HH:mm'))
  const powerData = chartData.value.map(item => item.pac || 0)
  const tempData = chartData.value.map(item => item.inverterTemperature || 0)

  return {
    ...restBaseOptions,
    xAxis: {
      type: 'category' as const,
      data: xAxisData,
    },
    yAxis: [
      {
        type: 'value' as const,
        name: '功率(kW)',
        position: 'left',
      },
      {
        type: 'value' as const,
        name: '温度(°C)',
        position: 'right',
      },
    ],
    series: [
      {
        name: '发电功率',
        type: 'line' as const,
        yAxisIndex: 0,
        data: powerData,
        smooth: true,
        itemStyle: {
          color: '#409eff',
        },
      },
      {
        name: '逆变器温度',
        type: 'line' as const,
        yAxisIndex: 1,
        data: tempData,
        smooth: true,
        itemStyle: {
          color: '#f56c6c',
        },
      },
    ],
    legend: {
      show: true,
      bottom: 0,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
    },
  }
})

// 解析MPPT信息
function parseMpptInfo(mpptInfoStr?: string): MpptInfo[] {
  if (!mpptInfoStr) return []
  try {
    return JSON.parse(mpptInfoStr)
  } catch {
    return []
  }
}

// 计算统计信息
const mpptStats = computed(() => {
  const mpptCount = mpptInfoList.value.length
  const stringCount = mpptInfoList.value.reduce((total, mppt) => total + mppt.pv.length, 0)
  const moduleCount = mpptInfoList.value.reduce(
    (total, mppt) => total + mppt.pv.reduce((pvTotal, pv) => pvTotal + pv.total, 0),
    0,
  )

  return {
    mpptCount,
    stringCount,
    moduleCount,
  }
})

// 开始编辑MPPT信息
function startEditMppt() {
  showMpptEditor.value = true
}

// 处理保存事件
function handleMpptSave(newMpptInfo: MpptInfo[]) {
  mpptInfoList.value = newMpptInfo
}

// 获取数据
async function fetchData() {
  if (!props.inverter.inverterSn) return

  chartLoading.value = true
  try {
    const today = dayjs().format('YYYY-MM-DD')
    const [chartResult, elecResult, mpptResult, radiationResult] = await Promise.all([
      getInverterDataList({ date: today, inverterSn: props.inverter.inverterSn }),
      getInverterElecData({ inverterSn: props.inverter.inverterSn }),
      getInverterMpptDataList({ date: today, inverterSn: props.inverter.inverterSn }),
      getInverterWeatherRadiationData({ date: today, inverterSn: props.inverter.inverterSn }),
    ])

    chartData.value = chartResult
    elecData.value = elecResult
    mpptData.value = mpptResult
    radiationData.value = radiationResult
  } finally {
    chartLoading.value = false
  }
}

onMounted(() => {
  // 解析MPPT信息
  if (props.inverter.mpptInfo) {
    mpptInfoList.value = parseMpptInfo(props.inverter.mpptInfo)
  }

  // 获取其他数据
  fetchData()
})
</script>

<template>
  <view class="inverter-detail-card">
    <!-- 逆变器基本信息 -->
    <view v-if="inverter.brandName || inverter.inverterModel || inverter.inverterSn" class="card">
      <view class="card__header">
        <view class="card__title-decorator" />
        <text class="card__title-text">逆变器{{ inverter.inverterSn || '' }}</text>
      </view>
      <view class="card__body">
        <view class="info-row">
          <text class="info-row__label">SN码</text>
          <text class="info-row__value">{{ inverter.inverterSn }}</text>
        </view>
        <view class="info-row">
          <text class="info-row__label">品牌</text>
          <text class="info-row__value">{{ inverter.brandName }}</text>
        </view>
        <view class="info-row">
          <text class="info-row__label">型号</text>
          <text class="info-row__value">{{ inverter.inverterModel || '-' }}</text>
        </view>
        <view class="info-row">
          <text class="info-row__label">图片</text>
          <wd-img
            v-if="inverter.imageUrl"
            class="info-row__value"
            :width="100"
            :height="100"
            :src="inverter.imageUrl"
            mode="aspectFill"
            :enable-preview="true"
          />
          <text v-else>-</text>
        </view>
      </view>
    </view>

    <!-- MPPT统计信息 -->
    <view class="card">
      <view class="card__header">
        <view class="card__title-decorator" />
        <text class="card__title-text">MPPT统计</text>
        <view class="card__header-action">
          <wd-button type="primary" size="small" :round="false" @click="startEditMppt">
            {{ mpptStats.mpptCount > 0 ? '编辑' : '新增' }}
          </wd-button>
        </view>
      </view>
      <view class="card__body">
        <view v-if="mpptStats.mpptCount > 0">
          <view class="info-row">
            <text class="info-row__label">MPPT数量</text>
            <text class="info-row__value">{{ mpptStats.mpptCount }}</text>
          </view>
          <view class="info-row">
            <text class="info-row__label">组串数量</text>
            <text class="info-row__value">{{ mpptStats.stringCount }}</text>
          </view>
          <view class="info-row">
            <text class="info-row__label">组串块数</text>
            <text class="info-row__value">{{ mpptStats.moduleCount }}</text>
          </view>
        </view>
        <view v-else class="no-data-tip">
          <text>暂无MPPT数据，点击新增按钮添加</text>
        </view>
      </view>
    </view>

    <!-- MPPT详细信息 -->
    <view v-if="mpptInfoList.length > 0" class="card">
      <view class="card__header">
        <view class="card__title-decorator" />
        <text class="card__title-text">MPPT详情</text>
      </view>
      <view class="card__body">
        <view v-for="(mppt, index) in mpptInfoList" :key="index" class="mppt-item">
          <view class="mppt-header">
            <text class="mppt-name">{{ mppt.name }}</text>
            <text class="mppt-total">总计: {{ mppt.total }}</text>
          </view>
          <view class="pv-list">
            <view v-for="(pv, pvIndex) in mppt.pv" :key="pvIndex" class="pv-item">
              <text class="pv-name">{{ pv.name }}</text>
              <text class="pv-total">{{ pv.total }}块</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 实时状态 -->
    <!-- <view class="card">
      <view class="card__header">
        <view class="card__title-decorator" />
        <text class="card__title-text">实时状态</text>
      </view>
      <view class="card__body">
        <view class="info-row">
          <text class="info-row__label">实时功率(kW)</text>
          <text class="info-row__value">{{ elecData.pac ?? '-' }}</text>
        </view>
        <view class="info-row">
          <text class="info-row__label">逆变器状态</text>
          <text class="info-row__value">{{ inverterStateText }}</text>
        </view>
      </view>
    </view> -->

    <!-- 发电量统计 -->
    <view class="card">
      <view class="card__header">
        <view class="card__title-decorator" />
        <text class="card__title-text">发电量统计</text>
      </view>
      <view class="card__body">
        <view class="info-row">
          <text class="info-row__label">装机容量(kW)</text>
          <text class="info-row__value">{{ elecData.power ?? '-' }}</text>
        </view>
        <view class="info-row">
          <text class="info-row__label">实时功率(kW)</text>
          <text class="info-row__value">{{ elecData.pac ?? '-' }}</text>
        </view>
        <view class="info-row">
          <text class="info-row__label">日发电量(kWh)</text>
          <text class="info-row__value">{{ elecData.elecDay ?? '-' }}</text>
        </view>
        <view class="info-row">
          <text class="info-row__label">月发电量(kWh)</text>
          <text class="info-row__value">{{ elecData.elecMonth ?? '-' }}</text>
        </view>
        <view class="info-row">
          <text class="info-row__label">年发电量(kWh)</text>
          <text class="info-row__value">{{ elecData.elecYear ?? '-' }}</text>
        </view>
        <view class="info-row">
          <text class="info-row__label">累计发电量(kWh)</text>
          <text class="info-row__value">{{ elecData.elecTotal ?? '-' }}</text>
        </view>
      </view>
    </view>

    <!-- 发电功率与温度图表 -->
    <view v-if="chartData.length > 0" class="card chart-card">
      <view class="card__header">
        <view class="card__title-decorator" />
        <text class="card__title-text">发电功率与温度</text>
      </view>
      <view class="card__body">
        <EchartWrapper :options="powerTempChartOption" :loading="chartLoading" height="240px" />
      </view>
    </view>

    <!-- 辐射度图表 -->
    <view v-if="radiationData.length > 0" class="card chart-card">
      <view class="card__header">
        <view class="card__title-decorator" />
        <text class="card__title-text">辐射</text>
      </view>
      <view class="card__body">
        <EchartWrapper :options="radiationChartOption" :loading="chartLoading" height="240px" />
      </view>
    </view>

    <!-- MPPT 功率图表 -->
    <view v-if="mpptData.length > 0" class="card chart-card">
      <view class="card__header">
        <view class="card__title-decorator" />
        <text class="card__title-text">MPPT 功率</text>
      </view>
      <view class="card__body">
        <EchartWrapper :options="mpptChartOption" :loading="chartLoading" height="240px" />
      </view>
    </view>

    <!-- MPPT编辑器弹窗 -->
    <MpptEditor
      v-model="showMpptEditor"
      :mppt-info-list="mpptInfoList"
      :inverter-id="inverter.id"
      @save="handleMpptSave"
    />
  </view>
</template>

<style scoped lang="scss">
.card {
  padding: 17px 18px;
  margin-bottom: 15px;
  background-color: #fff;
  border-radius: 6px;

  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }

  &__title-decorator {
    width: 3px;
    height: 15px;
    margin-right: 8px;
    background-color: #37acfe;
  }

  &__title-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #4b4b4b;
  }
}

.card__header-action {
  margin-left: auto;
}

.info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;

  &:last-child {
    margin-bottom: 0;
  }

  &__label {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    color: #91929e;
  }

  &__value {
    font-family: 'PingFang SC', sans-serif;
    font-size: 15px;
    color: #4b4b4b;
    text-align: right;
  }
}

.mppt-item {
  padding: 10px;
  margin-bottom: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;

  &:last-child {
    margin-bottom: 0;
  }
}

.mppt-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;

  .mppt-name {
    font-size: 14px;
    font-weight: bold;
    color: #303133;
  }

  .mppt-total {
    font-size: 12px;
    color: #606266;
  }
}

.pv-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.pv-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background-color: #e7f4ff;
  border-radius: 6px;

  .pv-name {
    margin-right: 4px;
    font-size: 12px;
    color: #409eff;
  }

  .pv-total {
    font-size: 12px;
    font-weight: bold;
    color: #409eff;
  }
}

.no-data-tip {
  padding: 20px 0;
  font-size: 14px;
  color: #909399;
  text-align: center;
}

.chart-card {
  .card__body {
    height: 240px;
    padding: 0;
  }
}
</style>
