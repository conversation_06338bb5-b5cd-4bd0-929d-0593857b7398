<script setup lang="ts">
import type { Station } from '@/types/api/Station'
import { businessType, isWarranty, pmSpecialFlag } from '@/constants/dict'
import { ref } from 'vue'

defineProps<{
  station: Station
  title?: string
}>()

const isStationInfoExpanded = ref(false)
const toggleStationInfo = () => {
  isStationInfoExpanded.value = !isStationInfoExpanded.value
}
</script>

<template>
  <view class="mb-3 rounded-lg bg-white p-4 shadow-sm">
    <view class="mb-3 flex items-center">
      <view class="accent-bar mr-2 h-4 w-1 bg-primary"></view>
      <text class="text-sm text-[#4B4B4B] font-bold">{{ title || '电站信息' }}</text>
    </view>
    <view class="detail-row">
      <text class="label">电站编码</text>
      <text class="value">{{ station.stationCode || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">业主姓名</text>
      <text class="value">{{ station.name || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">手机号</text>
      <text class="value">{{ station.phone || '-' }}</text>
    </view>

    <view v-show="isStationInfoExpanded">
      <view class="detail-row mt-1">
        <text class="label">电站模式</text>
        <text class="value">{{ station.mode || '-' }}</text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">关联资方</text>
        <text class="value">{{
          (station.specialFlag && pmSpecialFlag[station.specialFlag]) || '-'
        }}</text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">所属分中心</text>
        <text class="value">{{ station.subCenterName || '-' }}</text>
      </view>
      <!-- <view class="detail-row mt-1">
        <text class="label">运维商类别</text>
        <text class="value">{{ (station.op && opType[station.opType]) || '-' }}</text>
      </view> -->
      <view class="detail-row mt-1">
        <text class="label">运维商名称</text>
        <text class="value">{{ station.opName || '-' }}</text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">业务类型</text>
        <text class="value">{{
          (station.businessType && businessType[station.businessType]) || '-'
        }}</text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">是否质保期内</text>
        <text class="value">{{ isWarranty[station.isWarranty!] || '-' }}</text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">区域</text>
        <text class="value">{{
          [station.provinceName, station.cityName, station.regionName].filter(Boolean).join('') ||
          '-'
        }}</text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">详细地址</text>
        <text class="value">{{ station.address || '-' }}</text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">经纬度</text>
        <text class="value">{{ `${station.longitude || '-'},${station.latitude || '-'}` }}</text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">电站图片</text>
        <text class="value">
          <wd-img
            :width="100"
            :height="100"
            :src="station.roofHighImage"
            mode="aspectFill"
            :enable-preview="true"
          />
        </text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">电站图纸</text>
        <text class="value">
          <wd-img
            :width="100"
            :height="100"
            :src="station.roofSizeImage"
            mode="aspectFill"
            :enable-preview="true"
          />
        </text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">电站健康度</text>
        <text class="value">{{ station.stationScore || '-' }}</text>
      </view>
    </view>
    <view
      class="toggle-section mt-3 flex cursor-pointer items-center justify-center border-t border-gray-200 pt-3"
      @click="toggleStationInfo"
    >
      <text class="mr-1 text-sm text-primary">{{
        isStationInfoExpanded ? '收起信息' : '展开更多'
      }}</text>
      <wd-icon
        :name="isStationInfoExpanded ? 'arrow-up' : 'arrow-down'"
        size="16px"
        color="#3B82F6"
      ></wd-icon>
    </view>
  </view>
</template>

<style scoped>
.detail-row {
  display: flex;
  font-size: 14px;
  line-height: 20px;
}

.label {
  width: 88px;
  color: #666;
}

.value {
  color: #333;
  text-align: right;
  word-break: break-all;
}
</style>
