<script setup lang="ts">
import { updateInverterMpptInfo } from '@/api/station'
import { ref, watch } from 'vue'
import { useToast } from 'wot-design-uni'

interface MpptInfo {
  name: string
  total: number
  pv: Array<{
    name: string
    total: number
  }>
}

interface Props {
  modelValue: boolean
  mpptInfoList: MpptInfo[]
  inverterId: string | number | undefined
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'save'])

const toast = useToast()
const editingMpptList = ref<MpptInfo[]>([])

const showPopup = ref(props.modelValue)

watch(
  () => props.modelValue,
  newValue => {
    showPopup.value = newValue
    if (newValue) {
      if (props.mpptInfoList.length > 0) {
        editingMpptList.value = JSON.parse(JSON.stringify(props.mpptInfoList))
      } else {
        editingMpptList.value = [
          {
            name: 'mppt1',
            total: 0,
            pv: [{ name: 'pv1', total: 0 }],
          },
        ]
      }
    }
  },
)

watch(showPopup, newValue => {
  if (!newValue) {
    emit('update:modelValue', false)
  }
})

function addMppt() {
  const newMppt: MpptInfo = {
    name: `mppt${editingMpptList.value.length + 1}`,
    total: 0,
    pv: [{ name: 'pv1', total: 0 }],
  }
  editingMpptList.value.push(newMppt)
}

function removeMppt(index: number) {
  editingMpptList.value.splice(index, 1)
}

function addPv(mpptIndex: number) {
  const mppt = editingMpptList.value[mpptIndex]
  const existingNumbers = mppt.pv.map(pv => {
    const match = pv.name.match(/pv(\d+)/)
    return match ? Number.parseInt(match[1]) : 0
  })
  const nextNumber = Math.max(...existingNumbers, 0) + 1

  const newPv = {
    name: `pv${nextNumber}`,
    total: 0,
  }
  mppt.pv.push(newPv)
  updateMpptTotal(mpptIndex)
}

function removePv(mpptIndex: number, pvIndex: number) {
  editingMpptList.value[mpptIndex].pv.splice(pvIndex, 1)
  updateMpptTotal(mpptIndex)
}

function updateMpptTotal(mpptIndex: number) {
  const mppt = editingMpptList.value[mpptIndex]
  mppt.total = mppt.pv.reduce((sum, pv) => sum + pv.total, 0)
}

async function saveMpptInfo() {
  if (!props.inverterId) {
    toast.error('逆变器ID不存在')
    return
  }

  try {
    const mpptInfoStr = JSON.stringify(editingMpptList.value)
    await updateInverterMpptInfo({
      inverterId: props.inverterId,
      mpptInfo: mpptInfoStr,
    })

    toast.success('保存成功')
    emit('save', JSON.parse(JSON.stringify(editingMpptList.value)))
    cancelEdit()
  } catch {
    toast.error('保存失败，请重试')
  }
}

function cancelEdit() {
  emit('update:modelValue', false)
}
</script>

<template>
  <wd-popup
    v-model="showPopup"
    position="bottom"
    :safe-area-inset-bottom="true"
    @close="cancelEdit"
  >
    <view class="mppt-editor">
      <view class="mppt-editor__header">
        <text class="mppt-editor__title">
          {{ mpptInfoList.length > 0 ? '编辑MPPT信息' : '新增MPPT信息' }}
        </text>
        <wd-button type="text" :round="false" @click="cancelEdit">取消</wd-button>
      </view>
      <view class="mppt-editor__content">
        <scroll-view scroll-y class="mppt-editor__scroll">
          <view
            v-for="(mppt, mpptIndex) in editingMpptList"
            :key="mpptIndex"
            class="edit-mppt-item"
          >
            <view class="edit-mppt-header">
              <wd-input v-model="mppt.name" placeholder="MPPT名称" class="mppt-name-input" />
              <text class="mppt-total-text">总计: {{ mppt.total }}</text>
              <wd-button
                type="error"
                size="small"
                :round="false"
                :disabled="editingMpptList.length <= 1"
                @click="removeMppt(mpptIndex)"
              >
                删除
              </wd-button>
            </view>

            <view class="pv-edit-list">
              <view v-for="(pv, pvIndex) in mppt.pv" :key="pvIndex" class="pv-edit-item">
                <wd-input v-model="pv.name" placeholder="PV名称" class="pv-name-input" />
                <wd-input-number
                  v-model="pv.total"
                  :min="0"
                  :max="100"
                  class="pv-total-input"
                  @change="updateMpptTotal(mpptIndex)"
                />
                <wd-button
                  type="error"
                  size="small"
                  :round="false"
                  :disabled="mppt.pv.length <= 1"
                  @click="removePv(mpptIndex, pvIndex)"
                >
                  删除
                </wd-button>
              </view>

              <wd-button
                type="primary"
                size="small"
                :round="false"
                class="add-pv-btn"
                @click="addPv(mpptIndex)"
              >
                + 添加PV组串
              </wd-button>
            </view>
          </view>

          <wd-button type="primary" :round="false" class="add-mppt-btn" @click="addMppt">
            + 添加MPPT
          </wd-button>
        </scroll-view>
      </view>
      <view class="mppt-editor__footer">
        <wd-button type="primary" block :round="false" @click="saveMpptInfo">保存</wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<style scoped lang="scss">
.mppt-editor {
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  padding: 20px;

  &__header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
  }

  &__title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }

  &__content {
    flex: 1;
    overflow: hidden;
  }

  &__scroll {
    height: 100%;
    max-height: 50vh;
  }

  &__footer {
    flex-shrink: 0;
    padding-top: 20px;
  }
}

.edit-mppt-item {
  padding: 15px;
  margin-bottom: 20px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.edit-mppt-header {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 15px;

  .mppt-name-input {
    flex: 1;
  }

  .mppt-total-text {
    font-size: 14px;
    color: #606266;
    white-space: nowrap;
  }
}

.pv-edit-list {
  .pv-edit-item {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;

    .pv-name-input {
      flex: 1;
    }

    .pv-total-input {
      width: 100px;
    }
  }

  .add-pv-btn {
    width: 100%;
    margin-top: 10px;
  }
}

.add-mppt-btn {
  width: 100%;
  margin-top: 20px;
}
</style>
