<script lang="ts" setup>
import type { Station } from '@/types/api/Station'
import { useMapNavigation } from '@/composables/useMapNavigation'

const { openMapNavigation } = useMapNavigation()

const props = withDefaults(
  defineProps<{
    item: Station
    showNavigate?: boolean
  }>(),
  {
    showNavigate: true,
  },
)

const emit = defineEmits<{
  (e: 'click', item: Station): void
}>()

const handleClick = (item: Station) => {
  emit('click', item)
}
</script>

<template>
  <view class="station-card" @click="handleClick(item)">
    <view class="card-header">
      <text class="title">{{ item.stationCode }}</text>
      <view
        v-if="item.longitude && item.latitude && props.showNavigate"
        class="navigate-button"
        @click.stop="openMapNavigation(item)"
      >
        <text class="navigate-button__text">导航</text>
        <image src="/static/common/location.webp" class="navigate-button__icon" />
      </view>
    </view>
    <view class="card-meta">
      <text>{{ item.createdAt }}</text>
    </view>
    <view class="card-body">
      <view class="detail-item">
        <text class="label">电站名称</text>
        <text class="value">{{ item.name }}</text>
      </view>
      <view class="detail-item">
        <text class="label">电站地址</text>
        <text class="value">{{ item.address }}</text>
      </view>
      <view class="detail-item">
        <text class="label">联系方式</text>
        <text class="value">{{ item.phone }}</text>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.station-card {
  padding: 12px;
  margin-bottom: 10px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 5%);

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }

    .navigate-button {
      display: flex;
      gap: 4px;
      align-items: center;
      justify-content: center;
      width: 59px;
      height: 30px;
      padding-left: 4px;
      margin: 0;
      line-height: 1;
      background-color: rgb(55 172 254 / 10%);
      border: none;
      border-radius: 8px;

      &__text {
        font-size: 14px;
        line-height: 12px;
        color: $uni-color-primary;
      }

      &__icon {
        width: 25px;
        height: 25px;
      }

      &:active {
        opacity: 0.8;
      }
    }
  }

  .card-meta {
    padding-bottom: 8px;
    margin-bottom: 10px;
    font-size: 12px;
    color: #999;
    border-bottom: 1px solid #f0f0f0;
  }

  .card-body {
    .detail-item {
      display: flex;
      margin-bottom: 4px;
      font-size: 14px;
      line-height: 1.6;

      .label {
        flex-shrink: 0;
        width: 70px;
        margin-right: 8px;
        color: #666;
      }

      .value {
        flex: 1;
        color: #333;
        word-break: break-all;
      }
    }
  }

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
