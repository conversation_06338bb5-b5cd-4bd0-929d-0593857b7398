<script setup lang="ts">
import type { EChartsOption } from 'echarts/types/dist/shared'
import { defineEmits, defineProps } from 'vue'
import { LineChart } from '../../../components/charts'

const props = defineProps({
  powerData: {
    type: Array as () => number[],
    default: () => [],
  },
  irradianceData: {
    type: Array as () => number[],
    default: () => [],
  },
  timeLabels: {
    type: Array as () => string[],
    default: () => ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00'],
  },
  height: {
    type: String,
    default: '193px',
  },
  width: {
    type: String,
    default: '100%',
  },
  showLegend: {
    type: Boolean,
    default: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  className: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update', 'click'])

// 图表初始化完成
function onChartInit(chart: any) {
  emit('update', chart)
}

// 图表点击事件
function onChartClick(params: any) {
  emit('click', params)
}

// 构建Y轴配置
const yAxisConfig = [
  {
    type: 'value',
    name: 'KW',
    nameTextStyle: {
      padding: [0, 0, 0, 5],
      color: '#909399',
    },
    splitLine: {
      lineStyle: {
        color: '#EBEEF5',
      },
    },
    axisLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: '#909399',
    },
  },
  {
    type: 'value',
    name: 'W/m²',
    nameTextStyle: {
      padding: [0, 5, 0, 0],
      color: '#909399',
    },
    splitLine: {
      show: false,
    },
    axisLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: '#909399',
    },
  },
]

// 构建线数据
const lineData = [
  {
    name: '实时功率',
    data: props.powerData,
    color: '#409EFF',
    yAxisIndex: 0,
    smooth: true,
    lineStyle: {
      width: 2,
    },
  },
  {
    name: '辐照度',
    data: props.irradianceData,
    color: '#E6A23C',
    yAxisIndex: 1,
    smooth: true,
    lineStyle: {
      width: 2,
    },
  },
]

// 自定义图表选项
const customOptions: EChartsOption = {
  grid: {
    left: 35,
    right: 20,
    top: 10,
    bottom: 30,
  },
  xAxis: {
    axisLine: {
      lineStyle: {
        color: '#EBEEF5',
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: '#606266',
      margin: 15,
      fontSize: 12,
      fontWeight: 400,
    },
  },
}
</script>

<template>
  <view class="station-power-chart">
    <view v-if="showLegend" class="chart-legend">
      <view class="legend-item">
        <view class="legend-item__color-box legend-item__color-box--power"></view>
        <text class="legend-item__text">实时功率</text>
        <text class="legend-item__unit">KW</text>
      </view>
      <view class="legend-item">
        <view class="legend-item__color-box legend-item__color-box--irradiance"></view>
        <text class="legend-item__text">辐照度</text>
        <text class="legend-item__unit">W/m²</text>
      </view>
    </view>
    <LineChart
      :class-name="className"
      :x-axis-data="timeLabels"
      :lines="lineData"
      :y-axis="yAxisConfig"
      :height="height"
      :width="width"
      :loading="loading"
      :show-legend="false"
      :custom-options="customOptions"
      :show-fullscreen="true"
      @init="onChartInit"
      @click="onChartClick"
    />
  </view>
</template>

<style scoped lang="scss">
.station-power-chart {
  .chart-legend {
    display: flex;
    gap: 20px;
    justify-content: flex-start;
    padding-left: 16px;
    margin-bottom: 15px;
  }

  .legend-item {
    display: flex;
    align-items: center;

    &__color-box {
      width: 12px;
      height: 12px;
      margin-right: 5px;
      border-radius: 2px;

      &--power {
        background-color: #409eff;
      }

      &--irradiance {
        background-color: #e6a23c;
      }
    }

    &__text {
      margin-right: 5px;
      font-family: 'PingFang SC', sans-serif;
      font-size: 12px;
      color: #606266;
    }

    &__unit {
      font-family: 'PingFang SC', sans-serif;
      font-size: 12px;
      color: #909399;
    }
  }
}
</style>
