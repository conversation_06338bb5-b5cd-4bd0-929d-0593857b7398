<script setup lang="ts">
import type { Inverter } from '@/types/api/Station'
import { useChannel } from '@/composables/useChannel'
import { onLoad } from '@dcloudio/uni-app'
import { watchOnce } from '@vueuse/core'
import { ref } from 'vue'
import InverterDetailCard from './components/InverterDetailCard.vue'

const inverterSn = ref('')
const inverterInfo = ref<Inverter>({})

const { eventChannel } = useChannel()

// 处理接收到的inverter数据
function handleInverterData(data: { inverter: Inverter }) {
  if (data?.inverter) {
    inverterInfo.value = data.inverter
  }
}

onLoad(options => {
  if (options?.inverterSn) {
    inverterSn.value = options.inverterSn
  }
})

// 监听EventChannel的变化，当EventChannel可用时设置监听器（只执行一次）
watchOnce(eventChannel, newEventChannel => {
  if (newEventChannel) {
    newEventChannel.on('receiveInverterData', handleInverterData)
  }
})
</script>

<template>
  <view class="inverter-page">
    <InverterDetailCard v-if="inverterInfo.inverterSn" :inverter="inverterInfo" />
    <view v-else class="loading-state">
      <text>加载中...</text>
    </view>
  </view>
</template>

<style scoped lang="scss">
.inverter-page {
  min-height: 100vh;
  padding: 15px;
  background-color: #f5f5f5;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  font-size: 14px;
  color: #999;
}
</style>
