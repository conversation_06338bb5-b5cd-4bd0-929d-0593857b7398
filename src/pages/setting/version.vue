<script setup lang="ts">
import VersionUpdater from '@/components/VersionUpdater.vue'
import { getSystemInfo } from '@uni-helper/uni-promises'
import { ref } from 'vue'

const version = ref('1.0.0')
const versionUpdaterRef = ref<InstanceType<typeof VersionUpdater>>()
const showLatestVersionMessage = ref(false)

const checkUpdate = async () => {
  const hasUpdate = await versionUpdaterRef.value?.checkVersion(true)
  if (!hasUpdate) {
    showLatestVersionMessage.value = true
  }
  else {
    showLatestVersionMessage.value = false
  }
}

getSystemInfo().then((info) => {
  version.value = info.appVersion || '1.0.0'
})
</script>

<template>
  <view class="version-page">
    <view class="version-page__details">
      <view class="version-page__row">
        <text class="version-page__label">当前版本</text>
        <text class="version-page__value">v{{ version }}</text>
      </view>
      <view v-if="showLatestVersionMessage" class="version-page__row version-page__row--check-info">
        <view class="version-page__check-icon">
          <text class="version-page__check-icon-text">i</text>
        </view>
        <text class="version-page__check-text">已是最新版本</text>
      </view>
    </view>
    <wd-button
      type="primary"
      block
      :round="false"
      custom-class="version-page__update-button"
      @click="checkUpdate"
    >
      检查更新
    </wd-button>
    <VersionUpdater ref="versionUpdaterRef" :auto-check="false" />
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "关于版本"
  }
}
</route>

<style scoped lang="scss">
.version-page {
  min-height: 100vh;
  padding: 20px;
  background-color: #fff;

  &__details {
    margin-bottom: 36px;
  }

  &__row {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &--check-info {
      justify-content: flex-start;
      margin-top: 5px;
    }
  }

  &__label {
    font-size: 15px;
    color: #606266;
  }

  &__value {
    font-size: 15px;
    color: #303133;
  }

  &__check-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    margin-right: 6px;
    background-color: #37acfe;
    border-radius: 9999px;
  }

  &__check-icon-text {
    font-size: 10px;
    line-height: 1;
    color: #fff;
  }

  &__check-text {
    font-size: 13px;
    color: #37acfe;
  }

  &__update-button {
    height: 45px !important;
    font-size: 14px !important;
    border-radius: 7.5px !important;
  }
}
</style>
