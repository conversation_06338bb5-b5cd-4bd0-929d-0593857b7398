<script setup lang="ts">
import { ref } from 'vue'
import { useMessage } from 'wot-design-uni'

const props = defineProps<{
  selector?: string
  title?: string
  cancelText?: string
  confirmText?: string
}>()

const emit = defineEmits<{
  (e: 'confirm'): void
  (e: 'cancel'): void
}>()

const messageBox = useMessage(props.selector || 'default-dialog-box')

// 打开对话框
const open = async () => {
  try {
    const result = await messageBox.confirm({
      title: props.title || '提示',
      cancelButtonText: props.cancelText || '取消',
      confirmButtonText: props.confirmText || '确定'
    })

    if (result) {
      emit('confirm')
      return true
    }
  } catch (error: any) {
    if (error && error.action === 'cancel') {
      emit('cancel')
    }
    return false
  }
  return false
}

// 暴露方法给父组件
defineExpose({
  open
})
</script>

<template>
  <wd-message-box :selector="selector || 'default-dialog-box'" custom-class="custom-dialog-box">
    <slot></slot>
  </wd-message-box>
</template>

<style lang="scss" scoped>
:deep(.wd-popup-wrapper) {
  display: flex;
  justify-content: center;

  .wd-popup--center {
    top: 20%;
    left: unset;
    transform: unset;
  }
}

:deep(.wd-message-box__content) {
  height: max-content;
  max-height: max-content;
  padding: 24px!important;
}
</style>
