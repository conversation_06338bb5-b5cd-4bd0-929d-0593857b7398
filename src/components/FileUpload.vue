<script setup lang="ts">
import type {
  UploadFile,
  UploadProps,
  UploadMethod,
  UploadFileItem,
  UploadFormData,
  UploadRemoveEvent,
} from 'wot-design-uni/components/wd-upload/types'
import { computed, ref, getCurrentInstance, nextTick } from 'vue'
import { useAuthStore, useUserStore } from '@/store'
import { HDSBaseUrl, MerchantBaseUrl } from '@/constants'
import { createSelectorQuery, getLocation } from '@uni-helper/uni-promises'

interface Props extends /** @vue-ignore */ Partial<Omit<UploadProps, 'fileList'>> {
  watermarkText?: string | string[]
  disabled?: boolean
  limit?: number
  name?: string
  sourceType?: UploadProps['sourceType']
}

const props = withDefaults(defineProps<Props>(), {
  limit: 3,
  name: 'multipartFiles',
  disabled: false,
})

const fileList = defineModel<UploadFile[]>('fileList', { default: () => [] })

const emit = defineEmits<{
  (e: 'success', event: { file: UploadFile; fileList: UploadFile[]; response?: any }): void
  (e: 'remove', event: { file: UploadFile; fileList: UploadFile[] }): void
  (e: 'fail', event: { file: UploadFile; error: any; fileList: UploadFile[] }): void
}>()

const userInfo = useUserStore()
const authStore = useAuthStore()
const instance = getCurrentInstance()

const uploadTasks = new Map<string | number, UniApp.UploadTask>()

const canvasDynamicWidth = ref(300)
const canvasDynamicHeight = ref(150)

const header = computed(() => {
  return {
    Authorization: `Bearer ${authStore.token}`,
  }
})

const actionUrl = computed(() => {
  const urlMap: Record<string, string> = {
    haier: '/oss/image/upload.do',
    merchant: '/oss/image/upload.do',
  }
  const userType = userInfo.userInfo?.userType || 'haier'
  const prefixUrl = userType === 'merchant' ? MerchantBaseUrl : HDSBaseUrl
  return `${prefixUrl}${urlMap[userType]}`
})

async function applyWatermarkAndGetTempPath(
  filePath: string,
  text: string | string[],
): Promise<string> {
  return new Promise((resolve, reject) => {
    uni.getImageInfo({
      src: filePath,
      success: async imageInfo => {
        canvasDynamicWidth.value = imageInfo.width
        canvasDynamicHeight.value = imageInfo.height

        await nextTick()

        let attempts = 0
        const canvasId = 'watermarkCanvas'
        const maxAttempts = 40
        const pollInterval = 50

        while (attempts < maxAttempts) {
          const rect: UniApp.NodeInfo = await new Promise(resolveNode => {
            createSelectorQuery()
              .select(`#${canvasId}`)
              .boundingClientRect(node => {
                resolveNode(node as UniApp.NodeInfo)
              })
              .exec()
          })

          if (rect && rect.width === imageInfo.width && rect.height === imageInfo.height) {
            break
          }

          attempts++
          if (attempts >= maxAttempts) {
            console.warn(
              `Canvas dimensions did not match after ${maxAttempts} attempts. Expected: ${imageInfo.width}x${imageInfo.height}, Got: ${rect?.width}x${rect?.height}. Proceeding, but drawing might be incorrect.`,
            )
            break
          }
          await new Promise(r => setTimeout(r, pollInterval))
        }

        const ctx = uni.createCanvasContext(canvasId, instance?.proxy)

        if (!ctx) {
          reject(new Error('Failed to create canvas context'))
          return
        }

        ctx.drawImage(imageInfo.path, 0, 0, imageInfo.width, imageInfo.height)

        const fontSize = Math.max(12, Math.min(imageInfo.width, imageInfo.height) / 25)
        const edgePadding = fontSize * 0.5
        const textBgPadding = fontSize * 0.4

        ctx.setFontSize(fontSize)
        ctx.setTextAlign('left')
        ctx.setTextBaseline('top')

        let textBlockWidth = 0
        let textBlockHeight = 0
        const lineHeight = fontSize * 1.2

        if (Array.isArray(text) && text.length > 0) {
          text.forEach(line => {
            const metrics = ctx.measureText(line)
            const currentLineWidth =
              metrics && metrics.width > 0 ? metrics.width : line.length * fontSize * 0.65 // Adjusted fallback factor
            if (currentLineWidth > textBlockWidth) {
              textBlockWidth = currentLineWidth
            }
          })
          textBlockHeight = (text.length - 1) * lineHeight + fontSize
        } else if (!Array.isArray(text)) {
          const lineText = text as string
          const metrics = ctx.measureText(lineText)
          textBlockWidth =
            metrics && metrics.width > 0 ? metrics.width : lineText.length * fontSize * 0.65 // Adjusted fallback factor
          textBlockHeight = fontSize
        }
        if (textBlockWidth === 0 && Array.isArray(text) && text.length > 0 && text[0].length > 0) {
          textBlockWidth = text[0].length * fontSize * 0.65
        }

        const bgX = edgePadding
        const bgY = edgePadding
        const bgWidth = textBlockWidth + 2 * textBgPadding
        const bgHeight = textBlockHeight + 2 * textBgPadding

        ctx.setFillStyle('rgba(0, 0, 0, 0.7)')
        ctx.fillRect(bgX, bgY, bgWidth, bgHeight)

        ctx.setFillStyle('rgba(255, 255, 255, 0.85)')

        if (Array.isArray(text)) {
          let currentY = edgePadding + textBgPadding
          text.forEach(line => {
            ctx.fillText(line, edgePadding + textBgPadding, currentY)
            currentY += lineHeight
          })
        } else {
          ctx.fillText(text as string, edgePadding + textBgPadding, edgePadding + textBgPadding)
        }

        ctx.draw(false, () => {
          uni.canvasToTempFilePath(
            {
              canvasId,
              x: 0,
              y: 0,
              width: imageInfo.width,
              height: imageInfo.height,
              destWidth: imageInfo.width/2,
              destHeight: imageInfo.height/2,
              fileType: 'png',
              quality: 1,
              success: res => resolve(res.tempFilePath),
              fail: err => reject(err),
            },
            instance?.proxy,
          )
        })
      },
      fail: err => reject(err),
    })
  })
}

const customUploadMethod: UploadMethod = (
  file: UploadFileItem,
  formData: UploadFormData,
  options: {
    action: string
    header: Record<string, any>
    name: string
    fileName: string
    fileType: 'image' | 'video' | 'audio'
    statusCode: number
    abortPrevious?: boolean
    onSuccess: (
      res: UniApp.UploadFileSuccessCallbackResult,
      file: UploadFileItem,
      formData: UploadFormData,
    ) => void
    onError: (
      res: UniApp.GeneralCallbackResult,
      file: UploadFileItem,
      formData: UploadFormData,
    ) => void
    onProgress: (res: UniApp.OnProgressUpdateResult, file: UploadFileItem) => void
  },
) => {
  const performUpload = async () => {
    let filePathToUpload = file.url || (file as any).path
    const targetFileForUpload = fileList.value.find(f => f.uid === file.uid)

    if (!filePathToUpload) {
      const error = { errMsg: 'File path missing' }
      if (targetFileForUpload) {
        targetFileForUpload.status = 'fail'
        fileList.value = [...fileList.value]
      }
      options.onError(error, file, formData)
      emit('fail', { file, error, fileList: fileList.value })
      return
    }

    if (props.watermarkText) {
      try {
        const targetFile = fileList.value.find(f => f.uid === file.uid)
        if (targetFile) {
          targetFile.status = 'loading'
          targetFile.percent = 0
          fileList.value = [...fileList.value]
        }

        const watermarkData = Array.isArray(props.watermarkText)
          ? [...props.watermarkText]
          : [props.watermarkText]

        try {
          const location = await getLocation({ type: 'gcj02', isHighAccuracy: true })
          watermarkData.push(`经度: ${location.longitude}`)
          watermarkData.push(`纬度: ${location.latitude}`)
        } catch (e) {
          console.error('获取地理位置失败', e)
        }

        filePathToUpload = await applyWatermarkAndGetTempPath(filePathToUpload, watermarkData)
      } catch (error: any) {
        const targetFile = fileList.value.find(f => f.uid === file.uid)
        if (targetFile) {
          targetFile.status = 'fail'
          fileList.value = [...fileList.value]
        }
        options.onError(
          { errMsg: `Watermarking failed: ${error.message || error}` },
          file,
          formData,
        )
        emit('fail', { file, error, fileList: fileList.value })
        return
      }
    }

    if (targetFileForUpload) {
      targetFileForUpload.status = 'loading'
      targetFileForUpload.percent = 0
      fileList.value = [...fileList.value]
    }

    const uploadTask = uni.uploadFile({
      url: actionUrl.value,
      filePath: filePathToUpload,
      name: options.name || props.name,
      fileName: options.name || props.name,
      fileType: options.fileType,
      header: { ...header.value, ...options.header },
      formData,
      success: uploadFileRes => {
        if (uploadFileRes.statusCode === (options.statusCode || 200)) {
          options.onSuccess(uploadFileRes, file, formData)

          try {
            const responseData = JSON.parse(uploadFileRes.data as string)
            if (responseData.success && responseData.result && responseData.result.length > 0) {
              const imageUrl = responseData.result[0].imageUrl
              if (imageUrl) {
                file.url = imageUrl
              }
              const fileInCurrentList = fileList.value.find(f => f.uid === file.uid)
              if (fileInCurrentList) {
                fileInCurrentList.response = responseData
              }
              emit('success', { file, fileList: fileList.value, response: responseData })
            } else {
              const appError = {
                errMsg: responseData.message || 'Upload failed with non-success server response',
                responseData,
              }
              options.onError(appError, file, formData)
              emit('fail', { file, error: appError, fileList: fileList.value })
            }
          } catch (e: any) {
            const parseError = {
              errMsg: `Response parsing error: ${e.message || e}`,
              originalResponse: uploadFileRes.data,
            }
            options.onError(parseError, file, formData)
            emit('fail', { file, error: parseError, fileList: fileList.value })
          }
        } else {
          const httpError = {
            ...uploadFileRes,
            errMsg: uploadFileRes.errMsg || `Upload failed with status ${uploadFileRes.statusCode}`,
          }
          options.onError(httpError, file, formData)
          emit('fail', { file, error: httpError, fileList: fileList.value })
        }
        uploadTasks.delete(file.uid)
      },
      fail: err => {
        const uploadFailError = { ...err, errMsg: err.errMsg || 'Upload request failed' }
        options.onError(uploadFailError, file, formData)
        emit('fail', { file, error: uploadFailError, fileList: fileList.value })
        uploadTasks.delete(file.uid)
      },
    })

    uploadTask.onProgressUpdate(res => {
      options.onProgress(res, file)
      const fileInList = fileList.value.find(f => f.uid === file.uid)
      if (fileInList) {
        fileInList.percent = res.progress
      }
    })
    uploadTasks.set(file.uid!, uploadTask)
  }

  performUpload()
}

const onRemove = (event: UploadRemoveEvent) => {
  const removedFile = event.file
  const task = uploadTasks.get(removedFile.uid!)
  if (task) {
    task.abort()
    uploadTasks.delete(removedFile.uid!)
  }
  emit('remove', { file: removedFile, fileList: fileList.value })
}
</script>

<template>
  <view class="file-upload-container">
    <wd-upload
      v-bind="$attrs"
      v-model:file-list="fileList"
      :disabled="props.disabled"
      :limit="props.limit"
      :name="props.name"
      image-mode="aspectFill"
      :custom-class="`${props.disabled ? 'custom-upload is-disabled' : 'custom-upload'} `"
      :upload-method="customUploadMethod"
      :source-type="sourceType || ['camera']"
      @remove="onRemove"
    >
      <view class="uplaod-card">
        <wd-icon name="camera" custom-class="text-xl"></wd-icon>
      </view>
    </wd-upload>
    <canvas
      canvas-id="watermarkCanvas"
      :style="{ width: canvasDynamicWidth + 'px', height: canvasDynamicHeight + 'px' }"
      style="position: absolute; top: -9999px; left: -9999px; pointer-events: none; opacity: 0"
    ></canvas>
  </view>
</template>

<style scoped lang="scss">
.file-upload-container {
  width: 100%;
}

:deep(.custom-upload) {
  gap: 16px;

  &.is-disabled {
    .wd-upload__evoke-slot {
      display: none;
    }
  }

  .uplaod-card {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100px;
    background-color: var(--wot-color-gray-2);
    border-radius: 6px;
  }

  .wd-upload__preview,
  .wd-upload__list-item {
    width: 100px !important;
    height: 100px !important;
    margin: 0 !important;
  }
}
</style>
