import uniHelper from '@uni-helper/eslint-config'

export default uniHelper({
  uni: true,
  uniJson: true,
  unocss: true,
  vue: {
    overrides: {
      'vue/custom-event-name-casing': ['kebab-case' | 'camelCase'],
      'vue/singleline-html-element-content-newline': 'off',
      'vue/multiline-html-element-content-newline': 'off',
      'vue/max-attributes-per-line': [
        'error',
        {
          multiline: 4,
          singleline: 10,
        },
      ],
      'antfu/top-level-function': 'off',
      'vue/no-v-text-v-html-on-component': 'off',
      'vue/html-self-closing': 'off',
      'vue/operator-linebreak': 'off',
      'antfu/consistent-chaining': 'off',
      'ts/no-use-before-define': 'off',
    },
  },
})
